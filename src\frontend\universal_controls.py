"""
Universal Controls Widget - Centralized Data Management
Single source of truth for data fetching that automatically updates ALL tabs.
Implements enterprise-grade centralized architecture with zero-computation UI.
"""

from PyQt6.QtWidgets import (
    QWidget,
    QHBoxLayout,
    QVBoxLayout,
    QLabel,
    QLineEdit,
    QComboBox,
    QPushButton,
    QFrame,
    QInputDialog,
)
from PyQt6.QtCore import (
    pyqtSignal,
    Qt,
    QEvent,
    QObject,
    QRunnable,
    QThreadPool,
    pyqtSlot,
)
from PyQt6.QtGui import QFont, QMouseEvent
from typing import Dict, Any, Optional
import asyncio
import logging

# Import zero-computation architecture components
from backend.signals import signal_manager, TypedSlotDecorator
from backend.observability import get_structured_logger


class CentralizedDataFetchWorker(QRunnable):
    """
    Enterprise-grade centralized data fetching worker.
    Single source of truth for all market data fetching across the application.
    """

    class Signals(QObject):
        # Well-typed signals for enterprise-grade communication
        finished = pyqtSignal(dict)  # market_data: Dict[str, Any]
        error = pyqtSignal(str)  # error_message: str
        progress = pyqtSignal(str)  # status_message: str

    def __init__(self, data_service, ticker: str, timeframe: str, dtl: int, length: int, fwl_aggr: int = 1,
                 viewing_historical: bool = False, historical_index: int = None):
        super().__init__()
        self.signals = self.Signals()
        self.data_service = data_service
        self.ticker = ticker
        self.timeframe = timeframe
        self.dtl = dtl
        self.length = length
        self.fwl_aggr = fwl_aggr
        self.viewing_historical = viewing_historical
        self.historical_index = historical_index
        self.logger = get_structured_logger(f"CentralizedDataFetchWorker.{ticker}")

    def run(self):
        """
        Execute centralized data fetching using enterprise-grade async patterns.
        Fetches data once and broadcasts to all tabs automatically.
        """
        try:
            self.logger.info("Starting centralized market data fetch")
            self.signals.progress.emit(f"Fetching data for {self.ticker}...")

            # Use asyncio.run for proper async execution without blocking
            # This creates a new event loop that doesn't interfere with Qt
            result = asyncio.run(self._fetch_data_async())

            if result:
                self.logger.info("Centralized market data fetch completed successfully")
                self.signals.finished.emit(result)
            else:
                error_msg = "No data received from backend service"
                self.logger.warning(error_msg)
                self.signals.error.emit(error_msg)

        except Exception as e:
            error_msg = f"Centralized data fetch failed: {str(e)}"
            self.logger.error(error_msg)
            self.signals.error.emit(error_msg)

    async def _fetch_data_async(self):
        """
        Async data fetching method that properly handles coroutines.
        Uses the same data service method for consistency.
        """
        try:
            # Fetch data with rebasing (same as MarketOddsTab for consistency)
            result = await self.data_service.fetch_market_data_with_rebasing(
                self.ticker, self.timeframe, self.dtl, self.length, self.fwl_aggr,
                self.viewing_historical, self.historical_index
            )
            return result

        except Exception as e:
            self.logger.error(f"Async centralized data fetch error: {e}")
            raise


class PyQt6ComboBox(QComboBox):
    """Pure PyQt6 ComboBox with double-click custom input."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_styling()

    def setup_styling(self):
        """Setup using pure PyQt6 methods."""
        font = QFont("Segoe UI", 9)
        self.setFont(font)
        self.setFixedHeight(26)
        self.setMinimumWidth(80)

    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """Handle double-click for custom input."""
        if not self.isEditable() and event.button() == Qt.MouseButton.LeftButton:
            current_text = self.currentText()
            text, ok = QInputDialog.getText(
                self,
                "Custom Value",
                "Enter custom value:",
                QLineEdit.EchoMode.Normal,
                current_text,
            )
            if ok and text:
                self.setCurrentText(text)
                self.activated.emit(self.currentIndex())
        else:
            super().mouseDoubleClickEvent(event)


class UniversalControls(QWidget):
    """
    Centralized Universal Controls - Single Source of Truth for Data Management.
    Fetches data once and automatically broadcasts to ALL tabs.
    Implements enterprise-grade centralized architecture.
    """

    # Enterprise-grade signals for centralized data management
    data_fetch_started = pyqtSignal(str)  # ticker: str
    data_fetch_completed = pyqtSignal(dict)  # market_data: Dict[str, Any]
    data_fetch_failed = pyqtSignal(str, str)  # ticker: str, error: str
    data_fetch_progress = pyqtSignal(str, str)  # ticker: str, status: str

    def __init__(self, data_service, parent=None):
        super().__init__(parent)
        self.data_service = data_service
        self.logger = get_structured_logger("UniversalControls")
        self.signals = signal_manager

        # Enterprise-grade thread management
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(2)  # Limit concurrent data fetches

        # Current data cache for efficiency
        self.current_data: Optional[Dict[str, Any]] = None
        self.current_fetch_params: Optional[Dict[str, Any]] = None

        # Reference to main window for accessing volatility tab
        self.main_window = None

        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Initialize the user interface using pure PyQt6."""
        # Main layout
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 5, 10, 5)
        main_layout.setSpacing(0)

        # Add stretch before controls to center them
        main_layout.addStretch()

        # Controls container
        controls_layout = QHBoxLayout()
        controls_layout.setContentsMargins(0, 0, 0, 0)
        controls_layout.setSpacing(10)

        # Fonts
        label_font = QFont("Segoe UI", 9)
        label_font.setBold(True)

        control_font = QFont("Segoe UI", 9)

        # Create all controls with pure PyQt6

        # Ticker input
        ticker_label = QLabel("Ticker:")
        ticker_label.setFont(label_font)

        self.ticker_input = QLineEdit()
        self.ticker_input.setPlaceholderText("SPY")
        self.ticker_input.setText("SPY")
        self.ticker_input.setFont(control_font)
        self.ticker_input.setFixedHeight(26)
        self.ticker_input.setFixedWidth(80)

        # Timeframe dropdown
        timeframe_label = QLabel("Timeframe:")
        timeframe_label.setFont(label_font)

        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(["1m", "5m", "15m", "30m", "60m", "Daily"])
        self.timeframe_combo.setCurrentText("Daily")
        self.timeframe_combo.setFont(control_font)
        self.timeframe_combo.setFixedHeight(26)
        self.timeframe_combo.setMinimumWidth(80)

        # DTL dropdown
        dtl_label = QLabel("DTL:")
        dtl_label.setFont(label_font)

        self.dtl_combo = PyQt6ComboBox()
        self.dtl_combo.setEditable(True)
        dtl_values = [
            1,
            2,
            3,
            4,
            5,
            10,
            15,
            20,
            25,
            50,
            100,
            200,
            250,
            500,
            1000,
            1500,
            2000,
            2500,
            3000,
            3500,
            4000,
            4500,
            5000,
            6000,
            7000,
            8000,
            9000,
            10000,
            12000,
            15000,
        ]
        self.dtl_combo.addItems([str(val) for val in dtl_values])
        self.dtl_combo.setCurrentText("200")

        # Length dropdown
        length_label = QLabel("Length:")
        length_label.setFont(label_font)

        self.length_combo = PyQt6ComboBox()
        self.length_combo.setEditable(True)
        length_values = [1, 2, 3, 7, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 150, 200]
        self.length_combo.addItems([str(val) for val in length_values])
        self.length_combo.setCurrentText("1")

        # Fetch button
        self.fetch_button = QPushButton("Fetch Data")
        self.fetch_button.setFont(control_font)
        self.fetch_button.setFixedHeight(26)
        self.fetch_button.setMinimumWidth(80)

        # Add all controls to layout
        controls_layout.addWidget(ticker_label)
        controls_layout.addWidget(self.ticker_input)
        controls_layout.addWidget(timeframe_label)
        controls_layout.addWidget(self.timeframe_combo)
        controls_layout.addWidget(dtl_label)
        controls_layout.addWidget(self.dtl_combo)
        controls_layout.addWidget(length_label)
        controls_layout.addWidget(self.length_combo)
        controls_layout.addWidget(self.fetch_button)

        # Add controls to main layout
        controls_widget = QWidget()
        controls_widget.setLayout(controls_layout)
        main_layout.addWidget(controls_widget)
        main_layout.addStretch()

    def connect_signals(self):
        """Connect widget signals for centralized data management."""
        self.fetch_button.clicked.connect(self.on_fetch_data)

    def on_fetch_data(self):
        """
        Handle fetch data button click - Centralized Data Fetching.
        Single source of truth that fetches data once and updates ALL tabs.
        """
        ticker = self.ticker_input.text().strip().upper()
        timeframe = self.timeframe_combo.currentText()

        try:
            dtl = int(self.dtl_combo.currentText())
        except ValueError:
            dtl = 250  # Default fallback

        try:
            length = int(self.length_combo.currentText())
        except ValueError:
            length = 20  # Default fallback

        if ticker:
            self.logger.info(f"Starting centralized data fetch for {ticker}")

            # Store current fetch parameters
            self.current_fetch_params = {
                "ticker": ticker,
                "timeframe": timeframe,
                "dtl": dtl,
                "length": length
            }

            # Disable fetch button during operation
            self.disable_fetch_button()

            # Create and start centralized worker
            # Get FWL Aggr value from volatility statistics tab
            fwl_aggr = self.get_fwl_aggr_value()

            # Get historical context from volatility statistics tab
            viewing_historical, historical_index = self.get_historical_context()

            worker = CentralizedDataFetchWorker(
                self.data_service, ticker, timeframe, dtl, length, fwl_aggr,
                viewing_historical, historical_index
            )

            # Connect worker signals using enterprise-grade signal manager
            self.signals.connect_threadsafe(
                worker.signals.finished,
                self.on_centralized_data_received,
                Qt.ConnectionType.QueuedConnection,
            )
            self.signals.connect_threadsafe(
                worker.signals.error,
                self.on_centralized_data_error,
                Qt.ConnectionType.QueuedConnection,
            )
            self.signals.connect_threadsafe(
                worker.signals.progress,
                self.on_centralized_data_progress,
                Qt.ConnectionType.QueuedConnection,
            )

            # Start the centralized data fetch
            self.thread_pool.start(worker)

            # Emit centralized data fetch started signal
            self.data_fetch_started.emit(ticker)
            self.signals.market_data.data_fetch_started.emit(ticker)

    @TypedSlotDecorator.typed_slot(dict)
    def on_centralized_data_received(self, data: Dict[str, Any]):
        """
        Handle centralized data reception - Single Source of Truth.
        Automatically broadcasts data to ALL tabs via enterprise signal system.
        """
        try:
            self.logger.info("Centralized market data received successfully")

            # Cache the data for efficiency
            self.current_data = data

            # Broadcast to ALL tabs via centralized signal system
            self.signals.market_data.chart_data_updated.emit(data)

            # Emit completion signals
            self.data_fetch_completed.emit(data)

            # Re-enable fetch button
            self.enable_fetch_button()

            ticker = data.get("ticker", "Unknown")
            self.logger.info(f"Centralized data broadcast completed for {ticker}")

        except Exception as e:
            error_msg = f"Error processing centralized data: {str(e)}"
            self.logger.error(error_msg)
            self.on_centralized_data_error(error_msg)

    @TypedSlotDecorator.typed_slot(str)
    def on_centralized_data_error(self, error_msg: str):
        """
        Handle centralized data fetch error with enterprise-grade error handling.
        """
        self.logger.error(f"Centralized data fetch error: {error_msg}")

        # Broadcast error to all components
        ticker = self.current_fetch_params.get("ticker", "unknown") if self.current_fetch_params else "unknown"
        self.data_fetch_failed.emit(ticker, error_msg)
        self.signals.market_data.data_fetch_failed.emit(ticker, error_msg)

        # Re-enable fetch button
        self.enable_fetch_button()

    @TypedSlotDecorator.typed_slot(str)
    def on_centralized_data_progress(self, status_msg: str):
        """
        Handle centralized data fetch progress updates.
        """
        ticker = self.current_fetch_params.get("ticker", "unknown") if self.current_fetch_params else "unknown"
        self.logger.debug(f"Centralized data fetch progress: {status_msg}")

        # Broadcast progress to all components
        self.data_fetch_progress.emit(ticker, status_msg)
        self.signals.market_data.data_fetch_progress.emit(ticker, status_msg)

    def get_current_data(self) -> Optional[Dict[str, Any]]:
        """
        Get the current cached market data.
        Provides access to the single source of truth data.
        """
        return self.current_data

    def get_current_fetch_params(self) -> Optional[Dict[str, Any]]:
        """
        Get the current fetch parameters.
        Useful for tabs that need to know what data is currently loaded.
        """
        return self.current_fetch_params

    def refresh_current_data(self):
        """
        Refresh the current data using the same parameters.
        Convenient method for re-fetching with the same settings.
        """
        if self.current_fetch_params:
            params = self.current_fetch_params
            self.logger.info(f"Refreshing data for {params['ticker']}")

            # Update UI controls to match current params
            self.ticker_input.setText(params["ticker"])
            self.timeframe_combo.setCurrentText(params["timeframe"])
            self.dtl_combo.setCurrentText(str(params["dtl"]))
            self.length_combo.setCurrentText(str(params["length"]))

            # Trigger fetch
            self.on_fetch_data()
        else:
            self.logger.warning("No previous fetch parameters available for refresh")

    def get_current_values(self):
        """Get current values from all controls."""
        return {
            "ticker": self.ticker_input.text().strip().upper(),
            "timeframe": self.timeframe_combo.currentText(),
            "dtl": (
                int(self.dtl_combo.currentText())
                if self.dtl_combo.currentText().isdigit()
                else 250
            ),
            "length": (
                int(self.length_combo.currentText())
                if self.length_combo.currentText().isdigit()
                else 20
            ),
        }

    def enable_fetch_button(self):
        """Enable the fetch button."""
        self.fetch_button.setEnabled(True)

    def disable_fetch_button(self):
        """Disable the fetch button."""
        self.fetch_button.setEnabled(False)

    def get_fwl_aggr_value(self):
        """Get the FWL Aggr value from the volatility statistics tab."""
        try:
            if self.main_window and hasattr(self.main_window, 'volatility_statistics_tab'):
                volatility_tab = self.main_window.volatility_statistics_tab
                if hasattr(volatility_tab, 'get_fwl_aggr_value'):
                    return volatility_tab.get_fwl_aggr_value()
            return 1  # Default value
        except Exception as e:
            self.logger.error(f"Error getting FWL Aggr value: {e}")
            return 1  # Default value

    def get_historical_context(self):
        """Get historical context from the volatility statistics tab."""
        try:
            if self.main_window and hasattr(self.main_window, 'volatility_statistics_tab'):
                volatility_tab = self.main_window.volatility_statistics_tab
                viewing_historical = getattr(volatility_tab, 'viewing_historical', False)
                historical_index = getattr(volatility_tab, 'historical_index', None)
                return viewing_historical, historical_index
            return False, None  # Default values
        except Exception as e:
            self.logger.error(f"Error getting historical context: {e}")
            return False, None  # Default values

    def set_main_window(self, main_window):
        """Set reference to main window for accessing other tabs."""
        self.main_window = main_window
