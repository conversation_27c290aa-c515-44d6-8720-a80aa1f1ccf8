"""
Enterprise-grade buffer utilities for zero-copy data passing.
Implements proper buffer protocols and memory management for high-performance data processing.
"""

import sys
import logging
from typing import Any, Union, Optional, Protocol
from pathlib import Path
import numpy as np

# Add compute directory to path for C++ kernels
compute_dir = Path(__file__).parent.parent / "compute"
if compute_dir.exists() and str(compute_dir) not in sys.path:
    sys.path.insert(0, str(compute_dir))

try:
    import indicators
    CPP_KERNELS_AVAILABLE = True
    print(f"[OK] C++ kernels loaded successfully from {compute_dir}")
except (ImportError, OSError) as e:
    CPP_KERNELS_AVAILABLE = False
    print(f"[WARNING] C++ kernels not available: {e}")
    print("   The application will run with reduced functionality.")
    print("   To enable C++ kernels, ensure Visual C++ Redistributable is installed.")
    # Create a dummy indicators module for graceful fallback
    import types
    indicators = types.ModuleType('indicators')
    sys.modules['indicators'] = indicators


class BufferProtocol(Protocol):
    """Protocol for objects that support buffer interface."""

    def __array_interface__(self) -> dict: ...
    def __buffer__(self, flags: int) -> memoryview: ...


class ZeroCopyBufferManager:
    """
    Enterprise-grade zero-copy buffer manager that eliminates unnecessary data conversions.
    Provides efficient buffer passing between Python, NumPy, and C++ layers.
    """

    def __init__(self):
        self.logger = self._setup_logging()
        self._buffer_cache = {}

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging for buffer management."""
        logger = logging.getLogger("ZeroCopyBufferManager")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def get_buffer_view(self, data: Any) -> Union[memoryview, Any]:
        """
        Get a zero-copy buffer view of the data if possible.
        Falls back to original data if buffer interface is not available.
        """
        try:
            # Check if data supports buffer protocol (NumPy arrays, etc.)
            if hasattr(data, "__array_interface__"):
                # NumPy array - return memoryview for zero-copy access
                return memoryview(data)
            elif hasattr(data, "__buffer__"):
                # Object supports buffer protocol
                return data.__buffer__(0)
            elif isinstance(data, (bytes, bytearray)):
                # Built-in buffer types
                return memoryview(data)
            else:
                # No buffer interface available, return original data
                return data
        except Exception as e:
            self.logger.warning(
                f"Failed to create buffer view: {e}, falling back to original data"
            )
            return data

    def prepare_for_cpp_kernel(self, data: Any, kernel_name: str) -> Any:
        """
        Prepare data for C++ kernel consumption with optimal buffer passing.
        Uses zero-copy when possible, falls back to conversion only when necessary.
        """
        if not CPP_KERNELS_AVAILABLE:
            # No C++ kernels available, convert to list for Python fallback
            return self._safe_to_list(data)

        try:
            # Check if the specific C++ kernel supports buffer interface
            buffer_function_name = f"{kernel_name}_buffer"
            if hasattr(indicators, buffer_function_name):
                # Kernel supports buffer interface - use zero-copy
                if hasattr(data, "__array_interface__"):
                    self.logger.debug(f"Using zero-copy buffer for {kernel_name}")
                    return data  # Pass NumPy array directly
                elif hasattr(data, "__buffer__"):
                    return data  # Pass buffer-compatible object directly

            # Kernel doesn't support buffer interface or data isn't buffer-compatible
            # Fall back to list conversion only when necessary
            return self._safe_to_list(data)

        except Exception as e:
            self.logger.warning(f"Error preparing data for {kernel_name}: {e}")
            return self._safe_to_list(data)

    def _safe_to_list(self, data: Any) -> list:
        """
        Safely convert data to list only when absolutely necessary.
        Logs conversion for performance monitoring.
        """
        if isinstance(data, list):
            return data
        elif hasattr(data, "tolist"):
            self.logger.debug("Converting array to list (performance impact)")
            return data.tolist()
        elif hasattr(data, "__iter__"):
            return list(data)
        else:
            return [data]

    def call_cpp_kernel_with_optimal_buffers(
        self, kernel_name: str, *args, **kwargs
    ) -> Any:
        """
        Call C++ kernel with optimal buffer passing strategy.
        Automatically selects buffer or list interface based on kernel capabilities.
        """
        if not CPP_KERNELS_AVAILABLE:
            # Graceful fallback: return a mock result object
            class MockResult:
                def __init__(self):
                    self.success = False
                    self.data = []
                    self.error_message = "C++ kernels not available - using fallback"

            return MockResult()

        try:
            # Check if buffer version of kernel exists
            buffer_function_name = f"{kernel_name}_buffer"
            if hasattr(indicators, buffer_function_name):
                # Try buffer version first for zero-copy performance
                buffer_function = getattr(indicators, buffer_function_name)

                # Check if all arguments support buffer interface
                buffer_compatible = all(
                    hasattr(arg, "__array_interface__")
                    or isinstance(arg, (int, float, str, bool))
                    for arg in args
                )

                if buffer_compatible:
                    self.logger.debug(
                        f"Using zero-copy buffer interface for {kernel_name}"
                    )
                    return buffer_function(*args, **kwargs)

            # Fall back to list-based interface
            list_function = getattr(indicators, kernel_name)
            processed_args = [
                (
                    self.prepare_for_cpp_kernel(arg, kernel_name)
                    if not isinstance(arg, (int, float, str, bool))
                    else arg
                )
                for arg in args
            ]

            self.logger.debug(f"Using list interface for {kernel_name}")
            return list_function(*processed_args, **kwargs)

        except Exception as e:
            self.logger.error(f"Error calling C++ kernel {kernel_name}: {e}")
            raise RuntimeError(f"C++ kernel {kernel_name} failed: {e}") from e

    def optimize_data_for_serialization(self, data: Any) -> Any:
        """
        Optimize data for ProcessPoolExecutor serialization without unnecessary conversions.
        Uses shared memory when possible for large datasets.
        """
        try:
            if hasattr(data, "__array_interface__") and data.size > 1000:
                # Large NumPy array - consider shared memory for ProcessPoolExecutor
                # For now, return memoryview to avoid .tolist() conversion
                return memoryview(data)
            elif (
                hasattr(data, "tolist") and hasattr(data, "size") and data.size <= 1000
            ):
                # Small array - conversion acceptable for serialization
                return data.tolist()
            else:
                # Keep data as-is
                return data
        except Exception as e:
            self.logger.warning(f"Error optimizing data for serialization: {e}")
            return data

    def clear_cache(self):
        """Clear buffer cache to free memory."""
        self._buffer_cache.clear()
        self.logger.debug("Buffer cache cleared")


# Global buffer manager instance
buffer_manager = ZeroCopyBufferManager()
