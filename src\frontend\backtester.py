from PyQt6.QtWidgets import (QWidget, QVBoxLayout, Q<PERSON>plitter, QTabWidget,
                             QPushButton, QLabel, QFileDialog, QMessageBox, QProgressBar, QSpinBox,
                             QLineEdit, QComboBox, QCheckBox, QTableWidget, QTableWidgetItem)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor
import pyqtgraph as pg
import pandas as pd
import numpy as np
try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False

# Import backend services for independent zone calculations
from src.backend.volgraph_zones import VolgraphZonesService
from src.backend.pivot_zone import PivotZoneService
from src.backend.volatility_calculations_service import VolatilityCalculationsService
from src.backend.data_service import DataService


class CustomBacktesterTable(QTableWidget):
    """Custom table widget for backtester with day index highlighting."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_df = None
        self.setup_styling()

    def setup_styling(self):
        """Setup minimal styling to allow custom row colors."""
        # Use minimal stylesheet that won't interfere with item colors
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #555555;
                border: 1px solid #555555;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 4px;
                font-weight: bold;
            }
        """)
        # Disable alternating row colors to allow custom styling
        self.setAlternatingRowColors(False)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

    def setData(self, data_array, df=None):
        """Set table data and apply day index styling."""
        if data_array is None or len(data_array) == 0:
            return

        # Store the dataframe for analysis
        self.data_df = df

        # Set table dimensions
        self.setRowCount(len(data_array))
        self.setColumnCount(len(data_array[0]) if len(data_array) > 0 else 0)

        # Populate table with data (no styling here, will be applied later)
        for row_idx, row_data in enumerate(data_array):
            for col_idx, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                self.setItem(row_idx, col_idx, item)

        # Set up equal column widths that stretch across the full width
        self.setup_column_widths()

        # Apply day index styling after all items are created
        print(f"Data loaded: {len(data_array)} rows, applying day index styling...")
        self.apply_day_index_styling()

    def setup_column_widths(self):
        """Set up equal column widths that stretch across the full table width."""
        try:
            from PyQt6.QtWidgets import QHeaderView

            # Get the horizontal header
            header = self.horizontalHeader()

            # Set all columns to stretch equally
            header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

            print(f"Set up equal column widths for {self.columnCount()} columns")
        except Exception as e:
            print(f"Error setting up column widths: {e}")

    def apply_day_index_styling(self):
        """Apply white background and black text to day index rows (18:00 or closest after)."""
        if self.data_df is None or 'Date' not in self.data_df.columns:
            return

        try:
            # Find rows that represent the day index (18:00 or closest after for each day)
            day_index_rows = []

            if 'Time' in self.data_df.columns:
                # Reset index to ensure sequential row numbering that matches table rows
                df_reset = self.data_df.reset_index(drop=True)

                # Ensure Date column is datetime
                if not pd.api.types.is_datetime64_any_dtype(df_reset['Date']):
                    df_reset['Date'] = pd.to_datetime(df_reset['Date'], errors='coerce')

                # Convert time strings to time objects for comparison, handling various formats
                def parse_time_safe(time_str):
                    try:
                        if pd.isna(time_str) or time_str == '':
                            return None
                        # Try different time formats
                        for fmt in ['%H:%M:%S', '%H:%M', '%H:%M:%S.%f']:
                            try:
                                return pd.to_datetime(str(time_str), format=fmt).time()
                            except:
                                continue
                        return None
                    except:
                        return None

                df_reset['TimeForComparison'] = df_reset['Time'].apply(parse_time_safe)
                target_time = pd.to_datetime('18:00:00', format='%H:%M:%S').time()

                # Group by date and find 18:00 or closest time after 18:00 for each date
                for date, group in df_reset.groupby(df_reset['Date'].dt.date):
                    # Filter out rows with invalid times
                    valid_times = group.dropna(subset=['TimeForComparison'])
                    if valid_times.empty:
                        continue

                    # Filter times that are >= 18:00:00
                    after_1800 = valid_times[valid_times['TimeForComparison'] >= target_time]

                    if not after_1800.empty:
                        # Get the earliest time >= 18:00:00 by sorting and taking the first row
                        # Sort by time to get the earliest time >= 18:00
                        after_1800_sorted = after_1800.sort_values('TimeForComparison')
                        closest_row_idx = after_1800_sorted.index[0]  # Get the first (earliest) row index
                        day_index_rows.append(closest_row_idx)
                        print(f"Day {date}: Found day index at row {closest_row_idx}, time {group.loc[closest_row_idx, 'Time']}")
                    else:
                        # If no time >= 18:00, skip this day entirely - don't mark any row
                        print(f"Day {date}: Skipping - no 18:00+ time found")
            else:
                # If no time column, find first occurrence of each unique date
                df_reset = self.data_df.reset_index(drop=True)
                if not pd.api.types.is_datetime64_any_dtype(df_reset['Date']):
                    df_reset['Date'] = pd.to_datetime(df_reset['Date'], errors='coerce')

                prev_date = None
                for idx, row in df_reset.iterrows():
                    if pd.isna(row['Date']):
                        continue
                    current_date = row['Date'].date()
                    if current_date != prev_date:
                        day_index_rows.append(idx)
                        prev_date = current_date

            # Apply white background and black text to day index rows
            for row_idx in day_index_rows:
                if row_idx < self.rowCount():
                    # First, set all items in normal rows to dark theme
                    for col_idx in range(self.columnCount()):
                        item = self.item(row_idx, col_idx)
                        if item is None:
                            item = QTableWidgetItem("")
                            self.setItem(row_idx, col_idx, item)

                        # Force white background and black text for day index rows
                        white_color = QColor(255, 255, 255)  # Pure white
                        black_color = QColor(0, 0, 0)        # Pure black

                        item.setBackground(white_color)
                        item.setForeground(black_color)

                        # Also try setting data with UserRole to force the color
                        item.setData(Qt.ItemDataRole.BackgroundRole, white_color)
                        item.setData(Qt.ItemDataRole.ForegroundRole, black_color)

            # Set all non-day-index rows to dark theme
            all_rows = set(range(self.rowCount()))
            day_index_set = set(day_index_rows)
            normal_rows = all_rows - day_index_set

            for row_idx in normal_rows:
                for col_idx in range(self.columnCount()):
                    item = self.item(row_idx, col_idx)
                    if item:
                        dark_color = QColor(30, 30, 30)      # Dark background
                        white_color = QColor(255, 255, 255)  # White text

                        item.setBackground(dark_color)
                        item.setForeground(white_color)
                        item.setData(Qt.ItemDataRole.BackgroundRole, dark_color)
                        item.setData(Qt.ItemDataRole.ForegroundRole, white_color)

            print(f"✅ Applied day index styling to {len(day_index_rows)} rows (18:00 or closest after)")
            if len(day_index_rows) > 0:
                print(f"Day index rows: {day_index_rows[:10]}...")  # Show first 10

        except Exception as e:
            print(f"Error applying day index styling: {e}")
            import traceback
            traceback.print_exc()

class DataLoader(QThread):
    """Background thread for loading large datasets without freezing UI."""
    progress_updated = pyqtSignal(int)
    data_loaded = pyqtSignal(object, list)  # DataFrame, column names
    error_occurred = pyqtSignal(str)

    def __init__(self, file_path):
        super().__init__()
        self.file_path = file_path

    def run(self):
        try:
            # Load data based on file type
            if self.file_path.endswith('.csv'):
                # Use chunked reading for large CSV files with proper CSV parsing
                chunk_size = 50000
                chunks = []
                total_rows = sum(1 for line in open(self.file_path)) - 1  # Subtract header

                # CSV parsing parameters to handle spaces and quoted fields
                csv_params = {
                    'chunksize': chunk_size,
                    'skipinitialspace': True,  # Skip spaces after delimiter
                    'quotechar': '"',          # Handle quoted fields
                    'sep': ',',                # Comma separator
                    'engine': 'python'        # Use Python engine for better handling
                }

                for i, chunk in enumerate(pd.read_csv(self.file_path, **csv_params)):
                    # Clean column names by stripping whitespace
                    chunk.columns = chunk.columns.str.strip()
                    chunks.append(chunk)
                    progress = int((i * chunk_size / total_rows) * 100)
                    self.progress_updated.emit(min(progress, 100))

                df = pd.concat(chunks, ignore_index=True)

            elif self.file_path.endswith('.json'):
                self.progress_updated.emit(50)
                df = pd.read_json(self.file_path)

            elif self.file_path.endswith('.parquet'):
                self.progress_updated.emit(50)
                df = pd.read_parquet(self.file_path)
            else:
                self.error_occurred.emit("Unsupported file format")
                return

            self.progress_updated.emit(100)
            self.data_loaded.emit(df, df.columns.tolist())

        except Exception as e:
            self.error_occurred.emit(str(e))


class BacktesterTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.table_widget = None
        self.chart_widget = None
        self.current_data = None
        self.universal_controls_data = None  # Data from universal controls
        self.progress_bar = None
        self.data_loader = None
        self.dtb_input = None
        # Historical mode state
        self.viewing_historical = False
        self.historical_timestamp = None
        self.historical_index = None
        # Track zone graphics items so we can clear them reliably on refresh
        self._zone_items = []
        # Separate tracking for different zone types for visibility control
        self._intraday_zone_items = []  # Includes both pivot zones and intraday zones
        self._weekly_zone_items = []
        self._weekly_minavg_zone_items = []
        
        # Crosshair components (Market Odds style)
        self.crosshair_v = None  # Vertical crosshair line
        self.crosshair_h = None  # Horizontal crosshair line
        self.crosshair_active = False
        self.candlestick_data = []  # Store candlestick data for crosshair mapping
        
        # TradingView-style axis labels
        self.price_axis_label = None  # Price label on Y-axis
        self.time_axis_label = None   # Time label on X-axis
        
        # Invisible cursor for chart area
        self.invisible_cursor = None
        self.original_cursor = None

        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create tab widget for subtabs
        tab_widget = QTabWidget()

        # Chart tab (current layout)
        chart_tab = self.create_chart_tab()
        tab_widget.addTab(chart_tab, "Chart")

        # Analytics tab (empty for now)
        analytics_tab = QWidget()
        tab_widget.addTab(analytics_tab, "Analytics")

        layout.addWidget(tab_widget)

    def create_chart_tab(self):
        chart_widget = QWidget()
        layout = QVBoxLayout(chart_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # Main horizontal splitter (1/4 left, 3/4 right)
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left side - data insertion controls
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(10, 10, 10, 10)

        # Title
        title_label = QLabel("Insert Data")
        title_font = QFont("Segoe UI", 14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        left_layout.addWidget(title_label)

        # Insert data button
        insert_button = QPushButton("Select File")
        insert_button.clicked.connect(self.load_data_file)
        left_layout.addWidget(insert_button)

        # yfinance section
        if YFINANCE_AVAILABLE:
            yf_label = QLabel("yfinance Data:")
            yf_font = QFont("Segoe UI", 10)
            yf_font.setBold(True)
            yf_label.setFont(yf_font)
            left_layout.addWidget(yf_label)

            # Ticker input
            ticker_label = QLabel("Ticker:")
            left_layout.addWidget(ticker_label)

            self.ticker_input = QLineEdit()
            self.ticker_input.setPlaceholderText("e.g., AAPL, TSLA, SPY")
            left_layout.addWidget(self.ticker_input)

            # Interval selector
            interval_label = QLabel("Interval:")
            left_layout.addWidget(interval_label)

            self.interval_selector = QComboBox()
            self.interval_selector.addItems([
                "1m", "2m", "5m", "15m", "30m", "60m", "90m", "1h",
                "1d", "5d", "1wk", "1mo", "3mo"
            ])
            self.interval_selector.setCurrentText("1h")
            left_layout.addWidget(self.interval_selector)

            # Period input
            period_label = QLabel("Period:")
            left_layout.addWidget(period_label)

            self.period_selector = QComboBox()
            self.period_selector.addItems([
                "1d", "5d", "1mo", "3mo", "6mo", "1y", "2y", "5y", "10y", "ytd", "max"
            ])
            self.period_selector.setCurrentText("1mo")
            left_layout.addWidget(self.period_selector)

            # Forward fill checkbox
            self.forward_fill_checkbox = QCheckBox("Forward Fill Missing Data")
            self.forward_fill_checkbox.setChecked(True)
            left_layout.addWidget(self.forward_fill_checkbox)

            # Download button
            download_button = QPushButton("Download yfinance Data")
            download_button.clicked.connect(self.download_yfinance_data)
            left_layout.addWidget(download_button)
        else:
            # Show message if yfinance not available
            yf_unavailable = QLabel("yfinance not installed\npip install yfinance")
            small_font = QFont("Segoe UI", 8)
            yf_unavailable.setFont(small_font)
            left_layout.addWidget(yf_unavailable)

        # DTB (Days To Back) input
        dtb_label = QLabel("DTB:")
        left_layout.addWidget(dtb_label)

        self.dtb_input = QSpinBox()
        self.dtb_input.setRange(1, 9999)
        self.dtb_input.setValue(30)  # Default to 30 days
        self.dtb_input.setSuffix(" days")
        self.dtb_input.valueChanged.connect(self.update_chart_from_dtb)
        left_layout.addWidget(self.dtb_input)

        # Start backtest button
        start_backtest_button = QPushButton("Start Backtest")
        start_backtest_button.clicked.connect(self.start_backtest)
        left_layout.addWidget(start_backtest_button)

        # Zone visibility checkboxes
        self.show_intraday_zones_checkbox = QCheckBox("Show Intraday Zones")
        self.show_intraday_zones_checkbox.setChecked(True)  # Default to visible
        self.show_intraday_zones_checkbox.stateChanged.connect(self.toggle_intraday_zones_visibility)
        left_layout.addWidget(self.show_intraday_zones_checkbox)

        self.show_weekly_zones_checkbox = QCheckBox("Show Weekly Zones")
        self.show_weekly_zones_checkbox.setChecked(True)  # Default to visible
        self.show_weekly_zones_checkbox.stateChanged.connect(self.toggle_weekly_zones_visibility)
        left_layout.addWidget(self.show_weekly_zones_checkbox)
        # Checkbox to show only 5FWL MinAvg weekly zones
        self.show_minavg_weekly_zones_checkbox = QCheckBox("Show 5FWL MinAvg Zones")
        self.show_minavg_weekly_zones_checkbox.setChecked(False)
        self.show_minavg_weekly_zones_checkbox.stateChanged.connect(self.toggle_minavg_weekly_zones_visibility)
        left_layout.addWidget(self.show_minavg_weekly_zones_checkbox)


        # Progress bar for loading
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        left_layout.addWidget(self.progress_bar)

        left_layout.addStretch()
        main_splitter.addWidget(left_widget)

        # Right side - vertical splitter (4/5 top, 1/5 bottom)
        right_splitter = QSplitter(Qt.Orientation.Vertical)

        # Right top widget - chart
        right_top_widget = pg.PlotWidget()
        right_top_widget.setBackground('#1e1e1e')
        right_top_widget.setLabel('left', 'Price')
        right_top_widget.setLabel('bottom', 'Time')
        right_top_widget.showGrid(x=True, y=True)
        self.chart_widget = right_top_widget
        
        # Setup crosshair functionality
        self.setup_crosshair()
        
        right_splitter.addWidget(right_top_widget)

        # Right bottom widget - table
        right_bottom_widget = CustomBacktesterTable()
        self.table_widget = right_bottom_widget
        right_splitter.addWidget(right_bottom_widget)

        # Set right side proportions (4/5 top, 1/5 bottom)
        right_splitter.setSizes([800, 200])

        main_splitter.addWidget(right_splitter)

        # Set main proportions (1/4 left, 3/4 right)
        main_splitter.setSizes([250, 750])

        layout.addWidget(main_splitter)

        return chart_widget

    def setup_crosshair(self):
        """Setup crosshair functionality with Market Odds styling and TradingView-style axis labels."""
        try:
            if not self.chart_widget:
                return
                
            # Create invisible cursor for chart area (Market Odds style)
            self.invisible_cursor = self._create_invisible_cursor()
            self.original_cursor = self.chart_widget.cursor()
            
            # Create crosshair lines with Market Odds styling (white dashed lines)
            crosshair_pen = pg.mkPen("#FFFFFF", width=1, style=Qt.PenStyle.DashLine)
            self.crosshair_v = pg.InfiniteLine(angle=90, movable=False, pen=crosshair_pen)
            self.crosshair_h = pg.InfiniteLine(angle=0, movable=False, pen=crosshair_pen)
            
            # Initially hide crosshair
            self.crosshair_v.hide()
            self.crosshair_h.hide()
            
            # Add crosshair lines to chart
            self.chart_widget.addItem(self.crosshair_v, ignoreBounds=True)
            self.chart_widget.addItem(self.crosshair_h, ignoreBounds=True)
            
            # Create TradingView-style axis labels
            self._setup_axis_labels()
            
            # Connect mouse events
            self.chart_widget.scene().sigMouseMoved.connect(self.on_mouse_moved)
            
            # Enable mouse tracking
            self.chart_widget.setMouseTracking(True)
            
            print("Crosshair setup completed successfully")
            
        except Exception as e:
            print(f"Error setting up crosshair: {e}")
            import traceback
            traceback.print_exc()

    def _create_invisible_cursor(self):
        """Create an invisible cursor for chart areas (Market Odds style)."""
        from PyQt6.QtGui import QPixmap, QCursor
        # Create a 1x1 transparent pixmap
        pixmap = QPixmap(1, 1)
        pixmap.fill(Qt.GlobalColor.transparent)
        return QCursor(pixmap)

    def _setup_axis_labels(self):
        """Setup TradingView-style axis labels for price and time."""
        try:
            # Price label on Y-axis (left side)
            self.price_axis_label = pg.TextItem(
                text="",
                color="#FFFFFF",
                fill=pg.mkBrush(0, 0, 0, 200),  # Semi-transparent black background
                border=pg.mkPen("#FFFFFF", width=1),
                anchor=(1, 0.5)  # Right-center anchor (so text appears to the left of the anchor point)
            )
            self.price_axis_label.setVisible(False)
            self.chart_widget.addItem(self.price_axis_label, ignoreBounds=True)
            
            # Time label on X-axis (bottom)
            self.time_axis_label = pg.TextItem(
                text="",
                color="#FFFFFF",
                fill=pg.mkBrush(0, 0, 0, 200),  # Semi-transparent black background
                border=pg.mkPen("#FFFFFF", width=1),
                anchor=(0.5, 0)  # Center-top anchor
            )
            self.time_axis_label.setVisible(False)
            self.chart_widget.addItem(self.time_axis_label, ignoreBounds=True)
            
        except Exception as e:
            print(f"Error setting up axis labels: {e}")

    def on_mouse_moved(self, pos):
        """Handle mouse movement to update crosshair position (Market Odds style)."""
        try:
            if not self.chart_widget or not hasattr(self.chart_widget, 'plotItem'):
                return
                
            # Check if mouse is within the plot area
            if self.chart_widget.plotItem.vb.sceneBoundingRect().contains(pos):
                # Set invisible cursor for chart area
                if self.invisible_cursor:
                    self.chart_widget.setCursor(self.invisible_cursor)
                
                # Convert scene coordinates to data coordinates
                mouse_point = self.chart_widget.plotItem.vb.mapSceneToView(pos)
                x_pos = mouse_point.x()
                y_pos = mouse_point.y()
                
                # Show crosshair if not already visible
                if not self.crosshair_active:
                    self.crosshair_active = True
                    self.show_crosshair()
                
                # Update crosshair position
                self.update_crosshair_position(x_pos, y_pos)
                
            else:
                # Mouse left chart area - restore original cursor and hide crosshair
                if self.original_cursor:
                    self.chart_widget.setCursor(self.original_cursor)
                if self.crosshair_active:
                    self.crosshair_active = False
                    self.hide_crosshair()
                    
        except Exception as e:
            print(f"Error in mouse move handler: {e}")
            # Restore cursor on error
            if self.original_cursor:
                self.chart_widget.setCursor(self.original_cursor)
            self.hide_crosshair()

    def update_crosshair_position(self, x_pos, y_pos):
        """Update crosshair position and axis labels (Market Odds + TradingView style)."""
        try:
            # Update crosshair line positions
            self.crosshair_v.setPos(x_pos)
            self.crosshair_h.setPos(y_pos)
            
            # Update TradingView-style axis labels
            self._update_axis_labels(x_pos, y_pos)
            
        except Exception as e:
            print(f"Error updating crosshair position: {e}")

    def show_crosshair(self):
        """Show crosshair lines and axis labels (Market Odds style)."""
        try:
            self.crosshair_v.show()
            self.crosshair_h.show()
            
            if self.price_axis_label:
                self.price_axis_label.show()
            if self.time_axis_label:
                self.time_axis_label.show()
                
        except Exception as e:
            print(f"Error showing crosshair: {e}")

    def hide_crosshair(self):
        """Hide crosshair lines and axis labels (Market Odds style)."""
        try:
            self.crosshair_v.hide()
            self.crosshair_h.hide()
            
            if self.price_axis_label:
                self.price_axis_label.hide()
            if self.time_axis_label:
                self.time_axis_label.hide()
                
        except Exception as e:
            print(f"Error hiding crosshair: {e}")

    def _update_axis_labels(self, x_pos, y_pos):
        """Update TradingView-style axis labels with current price and time."""
        try:
            # Get chart view range for positioning
            view_range = self.chart_widget.plotItem.vb.viewRange()
            x_range = view_range[0]
            y_range = view_range[1]
            
            # Update price label (Y-axis, left side)
            if self.price_axis_label:
                price_text = f"{y_pos:.2f}"  # Round to 2 decimal places
                self.price_axis_label.setText(price_text)
                # Position further inside the chart area for better visibility
                label_x = x_range[0] + (x_range[1] - x_range[0]) * 0.05  # Move further right (5% from left edge)
                self.price_axis_label.setPos(label_x, y_pos)
            
            # Update time label (X-axis, bottom)
            if self.time_axis_label:
                time_text = self.map_x_to_time(x_pos)
                # Extract just the time part for cleaner display
                if "Time:" in time_text:
                    time_text = time_text.replace("Time: ", "")
                elif "Date:" in time_text:
                    time_text = time_text.replace("Date: ", "")
                elif "Index:" in time_text:
                    time_text = f"{x_pos:.1f}"
                
                self.time_axis_label.setText(time_text)
                # Position at optimal height from bottom of chart at crosshair X position
                label_y = y_range[0] + (y_range[1] - y_range[0]) * 0.05  # Sweet spot (5% from bottom edge)
                self.time_axis_label.setPos(x_pos, label_y)
                
        except Exception as e:
            print(f"Error updating axis labels: {e}")





    def map_x_to_time(self, x_pos):
        """Map x position back to time/date information."""
        try:
            if not hasattr(self, 'filtered_chart_data') or self.filtered_chart_data is None:
                return "Time: N/A"
                
            # Find the closest candlestick data point
            if hasattr(self, 'candlestick_data') and self.candlestick_data:
                import numpy as np
                x_positions = [item[0] for item in self.candlestick_data]
                
                if len(x_positions) == 0:
                    return "Time: N/A"
                    
                # Find closest x position
                closest_idx = np.argmin(np.abs(np.array(x_positions) - x_pos))
                
                # Ensure we don't go out of bounds
                if closest_idx >= len(self.filtered_chart_data):
                    closest_idx = len(self.filtered_chart_data) - 1
                elif closest_idx < 0:
                    closest_idx = 0
                
                # Try to map back to original data
                if hasattr(self, 'filtered_chart_data') and len(self.filtered_chart_data) > 0:
                    row = self.filtered_chart_data.iloc[closest_idx]
                    
                    # Format time display
                    if 'Date' in row and 'Time' in row:
                        # Handle datetime formatting more robustly
                        try:
                            if pd.notna(row['Date']):
                                if hasattr(row['Date'], 'strftime'):
                                    date_str = row['Date'].strftime('%Y-%m-%d')
                                else:
                                    date_str = str(row['Date']).split()[0]
                            else:
                                date_str = 'N/A'
                                
                            time_str = str(row['Time']) if pd.notna(row['Time']) else 'N/A'
                            return f"Time: {date_str} {time_str}"
                        except:
                            return f"Time: {str(row['Date'])} {str(row['Time'])}"
                    elif 'Date' in row:
                        try:
                            if pd.notna(row['Date']):
                                if hasattr(row['Date'], 'strftime'):
                                    date_str = row['Date'].strftime('%Y-%m-%d')
                                else:
                                    date_str = str(row['Date']).split()[0]
                            else:
                                date_str = 'N/A'
                            return f"Date: {date_str}"
                        except:
                            return f"Date: {str(row['Date'])}"
                        
            return f"Index: {x_pos:.2f}"
            
        except Exception as e:
            print(f"Error mapping x to time: {e}")
            return "Time: N/A"



    def reset_crosshair(self):
        """Reset crosshair state and hide all crosshair elements."""
        try:
            self.crosshair_active = False
            self.hide_crosshair()
            # Restore original cursor
            if self.original_cursor:
                self.chart_widget.setCursor(self.original_cursor)
        except Exception as e:
            print(f"Error resetting crosshair: {e}")

    def toggle_intraday_zones_visibility(self, state):
        """Toggle visibility of intraday zones (both pivot zones and intraday zones)."""
        try:
            visible = state == Qt.CheckState.Checked.value
            print(f"Toggling intraday zones visibility: {visible}")

            for item in self._intraday_zone_items:
                if hasattr(item, 'setVisible'):
                    item.setVisible(visible)
                elif hasattr(item, 'setOpacity'):
                    # For items that don't support setVisible, use opacity
                    item.setOpacity(1.0 if visible else 0.0)
        except Exception as e:
            print(f"Error toggling intraday zones visibility: {e}")

    def toggle_weekly_zones_visibility(self, state):
        """Toggle visibility of weekly zones."""
        try:
            visible = state == Qt.CheckState.Checked.value
            print(f"Toggling weekly zones visibility: {visible}")

            for item in self._weekly_zone_items:
                if hasattr(item, 'setVisible'):
                    item.setVisible(visible)
                elif hasattr(item, 'setOpacity'):
                    # For items that don't support setVisible, use opacity
                    item.setOpacity(1.0 if visible else 0.0)
        except Exception as e:
            print(f"Error toggling weekly zones visibility: {e}")

    def toggle_minavg_weekly_zones_visibility(self, state):
        """Show/hide 5FWL MinAvg weekly zones independently from regular weekly zones.
        If turned on and not yet rendered, render them across all days.
        """
        try:
            visible = state == Qt.CheckState.Checked.value
            print(f"Toggling 5FWL MinAvg weekly zones visibility: {visible}")

            # Toggle visibility of existing MinAvg zone items
            for item in self._weekly_minavg_zone_items:
                if hasattr(item, 'setVisible'):
                    item.setVisible(visible)
                elif hasattr(item, 'setOpacity'):
                    item.setOpacity(1.0 if visible else 0.0)

            # If enabling and nothing has been drawn yet, render them
            if visible and not self._weekly_minavg_zone_items:
                self.refresh_minavg_weekly_zones()
        except Exception as e:
            print(f"Error toggling 5FWL MinAvg weekly zones visibility: {e}")

    def refresh_minavg_weekly_zones(self):
        """Compute and render MinAvg-only weekly zones for all chart day indices.
        Uses the same per-day historical cutoff logic as weekly zones.
        """
        try:
            if not self.chart_widget:
                return

            # Remove existing MinAvg weekly zone items
            try:
                for item in list(self._weekly_minavg_zone_items):
                    try:
                        if hasattr(self.chart_widget, 'removeItem'):
                            self.chart_widget.removeItem(item)
                    except Exception:
                        pass
            finally:
                self._weekly_minavg_zone_items = []

            # Snapshot historical state to restore later
            prev_viewing = getattr(self, 'viewing_historical', False)
            prev_ts = getattr(self, 'historical_timestamp', None)
            prev_idx = getattr(self, 'historical_index', None)

            # Build per-day timestamps
            day_index_times = self._get_chart_day_index_timestamps()
            if not day_index_times:
                print("No day-index timestamps found; cannot refresh MinAvg weekly zones")
                return

            # Compute MinAvg weekly zones day-by-day (keep original daily behavior)
            for day_index, ts in enumerate(day_index_times):
                try:
                    # Use the same per-day timestamp as intraday zones for this date
                    cutoff_idx = self._find_cutoff_index_for_timestamp(ts)
                    if cutoff_idx is None:
                        continue

                    # Enable historical filtering for this day
                    self.viewing_historical = True
                    self.historical_timestamp = ts
                    self.historical_index = cutoff_idx

                    # Compute weekly zones using backend (respects historical filter)
                    weekly_result = self.calculate_weekly_zones_independently()
                    if weekly_result and isinstance(weekly_result, dict):
                        zones = weekly_result.get('zones', []) or []
                        zones = [z for z in zones if str(z.get('stat')) == 'min_avg']
                        if zones:
                            self._add_weekly_minavg_zones_to_day(zones, day_index)
                except Exception as e_day:
                    print(f"Error refreshing MinAvg weekly zones for day {day_index}: {e_day}")

            # Restore previous historical mode state
            self.viewing_historical = prev_viewing
            self.historical_timestamp = prev_ts
            self.historical_index = prev_idx
        except Exception as e:
            print(f"Error in refresh_minavg_weekly_zones: {e}")

    def load_data_file(self):
        """Load data from CSV, JSON, or Parquet file and display in table."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Data File",
            "",
            "Data Files (*.csv *.json *.parquet);;CSV Files (*.csv);;JSON Files (*.json);;Parquet Files (*.parquet)"
        )

        if file_path:
            # Show progress bar and start background loading
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # Create and start data loader thread
            self.data_loader = DataLoader(file_path)
            self.data_loader.progress_updated.connect(self.update_progress)
            self.data_loader.data_loaded.connect(self.on_data_loaded)
            self.data_loader.error_occurred.connect(self.on_load_error)
            self.data_loader.start()

    def update_progress(self, value):
        """Update progress bar."""
        self.progress_bar.setValue(value)

    def on_data_loaded(self, df, columns):
        """Handle successful data loading."""
        try:
            # Ensure Date column is properly formatted as datetime if it exists
            if 'Date' in df.columns:
                # Try to convert Date column to datetime (timezone-naive for consistency)
                df['Date'] = pd.to_datetime(df['Date'], errors='coerce')

                # Ensure timezone-naive for consistency with yfinance data
                if hasattr(df['Date'].dtype, 'tz') and df['Date'].dtype.tz is not None:
                    print("Converting CSV Date column to timezone-naive...")
                    df['Date'] = df['Date'].dt.tz_localize(None)

                # Check for NaT values created by coercion
                nat_count = df['Date'].isnull().sum()
                if nat_count > 0:
                    print(f"Warning: {nat_count} invalid dates converted to NaT during datetime conversion")

                    # Apply forward fill to handle NaT values
                    print("Applying forward fill to Date column to handle NaT values...")
                    df['Date'] = df['Date'].ffill()

                    # If still have NaT at the beginning, try backward fill
                    remaining_nat = df['Date'].isnull().sum()
                    if remaining_nat > 0:
                        print(f"Still have {remaining_nat} NaT values, applying backward fill...")
                        df['Date'] = df['Date'].bfill()

                        # Final check
                        final_nat = df['Date'].isnull().sum()
                        if final_nat > 0:
                            print(f"Warning: {final_nat} Date values could not be filled and remain as NaT")
                        else:
                            print("✅ All Date NaT values successfully filled")
                    else:
                        print("✅ All Date NaT values successfully forward filled")

                print(f"Converted Date column to datetime. Sample dates: {df['Date'].head(3).tolist()}")


            self.current_data = df

            # Show information about loaded data
            QMessageBox.information(
                self,
                "Data Loaded",
                f"Loaded {len(df):,} rows. Full dataset is available for analysis.\n"
                f"Use DTB (Days To Back) control to filter data display."
            )

            # Update candlestick chart and table with DTB-filtered dataset
            # Reset historical mode when a new file is loaded
            self._reset_historical_mode("new file loaded")
            self.update_chart_from_dtb()

            self.progress_bar.setVisible(False)

        except Exception as e:
            self.on_load_error(f"Error displaying data: {str(e)}")

    def on_load_error(self, error_message):
        """Handle loading errors."""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "Error", f"Failed to load file: {error_message}")

    def download_yfinance_data(self):
        """Download data from yfinance with forward fill option."""
        if not YFINANCE_AVAILABLE:
            QMessageBox.warning(self, "Error", "yfinance is not installed. Please install it with: pip install yfinance")
            return

        ticker = self.ticker_input.text().strip().upper()
        if not ticker:
            QMessageBox.warning(self, "Error", "Please enter a ticker symbol")
            return

        try:
            # Show progress
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(25)

            # Get parameters
            interval = self.interval_selector.currentText()
            period = self.period_selector.currentText()
            forward_fill = self.forward_fill_checkbox.isChecked()

            print(f"Downloading {ticker} data: period={period}, interval={interval}, forward_fill={forward_fill}")

            # Download data
            import yfinance as yf
            ticker_obj = yf.Ticker(ticker)
            data = ticker_obj.history(period=period, interval=interval)

            self.progress_bar.setValue(50)

            if data.empty:
                QMessageBox.warning(self, "Error", f"No data found for ticker {ticker}")
                self.progress_bar.setVisible(False)
                return

            # Reset index to get Date as column
            data = data.reset_index()

            # Rename columns to match CSV format
            column_mapping = {
                'Datetime': 'Date',
                'Date': 'Date',
                'Open': 'Open',
                'High': 'High',
                'Low': 'Low',
                'Close': 'Last',  # Map Close to Last
                'Volume': 'Volume'
            }

            # Only rename columns that exist
            existing_mapping = {k: v for k, v in column_mapping.items() if k in data.columns}
            data = data.rename(columns=existing_mapping)

            # Verify Date column exists and has valid data
            if 'Date' in data.columns:
                print(f"yfinance Date column created successfully with {len(data)} rows")
            else:
                print("❌ WARNING: No Date column found after yfinance processing!")

            self.progress_bar.setValue(75)

            # Add Time column if we have datetime but keep Date as datetime for proper filtering
            if 'Date' in data.columns:
                if hasattr(data['Date'].iloc[0], 'time'):
                    # Extract time component
                    data['Time'] = data['Date'].dt.strftime('%H:%M:%S')
                    # Convert to timezone-naive datetime for consistency
                    if hasattr(data['Date'].dtype, 'tz') and data['Date'].dtype.tz is not None:
                        data['Date'] = data['Date'].dt.tz_localize(None)
                    data['Date'] = pd.to_datetime(data['Date'])
                    print(f"✅ Extracted time component from intraday yfinance data")
                else:
                    # Daily data - set time to market close
                    data['Time'] = '16:00:00'
                    # Convert to timezone-naive datetime for consistency
                    if hasattr(data['Date'].dtype, 'tz') and data['Date'].dtype.tz is not None:
                        data['Date'] = data['Date'].dt.tz_localize(None)
                    data['Date'] = pd.to_datetime(data['Date'])
                    print(f"✅ Added default time for daily yfinance data")

                # Final verification
                if data['Date'].isnull().sum() > 0:
                    print(f"⚠️  Warning: {data['Date'].isnull().sum()} null Date values after processing")
                else:
                    print(f"✅ Date column processed successfully: {len(data)} valid dates")



            # Forward fill missing data if requested
            if forward_fill:
                print("Applying forward fill to missing data...")

                # Check for missing data before forward fill
                missing_before = data.isnull().sum()
                if missing_before.sum() > 0:
                    print(f"Missing data before forward fill:")
                    for col, count in missing_before.items():
                        if count > 0:
                            print(f"  {col}: {count} missing values")

                # Forward fill numeric columns
                numeric_columns = ['Open', 'High', 'Low', 'Last', 'Volume']
                existing_numeric = [col for col in numeric_columns if col in data.columns]
                if existing_numeric:
                    # Use modern pandas syntax instead of deprecated method
                    data[existing_numeric] = data[existing_numeric].ffill()
                    print(f"Applied forward fill to numeric columns: {existing_numeric}")

                # Forward fill Date and Time columns if they exist and have missing values
                datetime_columns = ['Date', 'Time']
                existing_datetime = [col for col in datetime_columns if col in data.columns]
                if existing_datetime:
                    for col in existing_datetime:
                        if data[col].isnull().any():
                            # For Date column, also try backward fill if forward fill doesn't work
                            # (in case the first values are NaT)
                            if col == 'Date':
                                # First try forward fill
                                data[col] = data[col].ffill()
                                # If still have NaT at the beginning, try backward fill
                                if data[col].isnull().any():
                                    data[col] = data[col].bfill()
                                    print(f"Applied forward + backward fill to {col} column")
                                else:
                                    print(f"Applied forward fill to {col} column")
                            else:
                                # For Time column, just forward fill
                                data[col] = data[col].ffill()
                                print(f"Applied forward fill to {col} column")

                # Check for missing data after forward fill
                missing_after = data.isnull().sum()
                if missing_after.sum() > 0:
                    print(f"Missing data after forward fill:")
                    for col, count in missing_after.items():
                        if count > 0:
                            print(f"  {col}: {count} missing values")
                else:
                    print("✅ All missing data successfully forward filled")

            # Add missing columns with default values if needed
            required_cols = ['Open', 'High', 'Low', 'Last', 'Volume']
            for col in required_cols:
                if col not in data.columns:
                    if col == 'Volume':
                        data[col] = 0
                    else:
                        # For OHLC, use Close price if available
                        if 'Last' in data.columns:
                            data[col] = data['Last']
                        else:
                            data[col] = 0

            # Append to existing data or store as new data
            if self.current_data is not None and len(self.current_data) > 0:
                # Append yfinance data AFTER existing CSV data
                print(f"Appending {len(data)} yfinance rows to {len(self.current_data)} existing rows")

                # Ensure column compatibility
                existing_cols = set(self.current_data.columns)
                new_cols = set(data.columns)

                # Add missing columns to existing data
                for col in new_cols - existing_cols:
                    if col == 'Volume':
                        self.current_data[col] = 0
                    elif col == 'Date':
                        # For Date column, use a proper datetime default or forward fill from yfinance data
                        if len(data) > 0 and 'Date' in data.columns:
                            # Use the first valid date from yfinance data
                            first_valid_date = data['Date'].dropna().iloc[0] if not data['Date'].dropna().empty else pd.Timestamp.now()
                            self.current_data[col] = first_valid_date
                        else:
                            self.current_data[col] = pd.Timestamp.now()
                    elif col == 'Time':
                        # For Time column, use a default time
                        self.current_data[col] = '16:00:00'
                    else:
                        # For other columns, use Last price as fallback
                        self.current_data[col] = self.current_data.get('Last', 0)

                # Add missing columns to new data
                for col in existing_cols - new_cols:
                    if col == 'Volume':
                        data[col] = 0
                    elif col == 'Date':
                        # For Date column, use a proper datetime default
                        if len(self.current_data) > 0 and 'Date' in self.current_data.columns:
                            # Use the last valid date from existing data
                            last_valid_date = self.current_data['Date'].dropna().iloc[-1] if not self.current_data['Date'].dropna().empty else pd.Timestamp.now()
                            data[col] = last_valid_date
                        else:
                            data[col] = pd.Timestamp.now()
                    elif col == 'Time':
                        # For Time column, use a default time
                        data[col] = '16:00:00'
                    else:
                        # For other columns, use Last price as fallback
                        data[col] = data.get('Last', 0)

                # Ensure both datasets have compatible Date column dtypes before concatenation
                if 'Date' in self.current_data.columns and 'Date' in data.columns:
                    # Convert both to the same timezone-naive datetime format
                    if hasattr(self.current_data['Date'].dtype, 'tz') and self.current_data['Date'].dtype.tz is not None:
                        self.current_data['Date'] = self.current_data['Date'].dt.tz_localize(None)

                    if hasattr(data['Date'].dtype, 'tz') and data['Date'].dtype.tz is not None:
                        data['Date'] = data['Date'].dt.tz_localize(None)

                    # Ensure both are datetime64[ns]
                    self.current_data['Date'] = pd.to_datetime(self.current_data['Date'])
                    data['Date'] = pd.to_datetime(data['Date'])

                # Concatenate: existing data first, then yfinance data
                self.current_data = pd.concat([self.current_data, data], ignore_index=True)
                print(f"Combined dataset now has {len(self.current_data)} total rows")

                # Verify Date column integrity after concatenation
                if 'Date' in self.current_data.columns:
                    date_null_count = self.current_data['Date'].isnull().sum()
                    if date_null_count > 0:
                        print(f"⚠️  Found {date_null_count} null Date values after concatenation, applying forward fill...")
                        self.current_data['Date'] = self.current_data['Date'].ffill().bfill()
                        remaining_nulls = self.current_data['Date'].isnull().sum()
                        if remaining_nulls == 0:
                            print("✅ All Date null values fixed after concatenation")
                        else:
                            print(f"⚠️  Still have {remaining_nulls} null Date values after fill")
                    else:
                        print("✅ Date column integrity maintained after concatenation")
            else:
                # No existing data, use yfinance data as base
                self.current_data = data
                print(f"Using yfinance data as base dataset: {len(data)} rows")

            self.progress_bar.setValue(100)

            # Update displays
            # Reset historical mode when new/downloaded data is applied
            self._reset_historical_mode("yfinance data downloaded/appended")
            self.update_chart_from_dtb()

            # Update table display using the combined dataset
            total_rows = len(self.current_data)
            yf_rows = len(data)

            QMessageBox.information(
                self,
                "yfinance Data Appended",
                f"Downloaded {yf_rows:,} rows for {ticker} and appended to existing data.\n"
                f"Total dataset: {total_rows:,} rows.\n"
                f"Use DTB (Days To Back) control to filter data display.\n"
                f"Forward fill: {'Enabled' if forward_fill else 'Disabled'}"
            )

            self.progress_bar.setVisible(False)

        except Exception as e:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "Error", f"Failed to download yfinance data: {str(e)}")
            print(f"yfinance download error: {e}")

    def start_backtest(self):
        """Start backtest functionality - creates zones for each chart day index extending 0.9x across each day."""
        print("Start Backtest button clicked")

        # Check if data is available from universal controls
        if self.universal_controls_data is None:
            QMessageBox.warning(
                self,
                "No Data Available",
                "No data has been fetched from Universal Controls.\n\n"
                "Please use the Universal Controls at the top of the application to fetch market data first."
            )
            return

        # Check if we have the required data structure
        if not isinstance(self.universal_controls_data, dict):
            QMessageBox.warning(
                self,
                "Invalid Data",
                "Invalid data format received from Universal Controls.\n\n"
                "Please fetch new data using Universal Controls."
            )
            return

        ticker = self.universal_controls_data.get("ticker", "Unknown")
        timeframe = self.universal_controls_data.get("timeframe", "Unknown")

        # Create zones for each chart day index with 0.9x extension
        try:
            self.create_zones_for_all_days()
        except Exception as e:
            print(f"Error creating zones for all days: {e}")
            QMessageBox.warning(
                self,
                "Zone Creation Error",
                f"Failed to create zones for all days: {str(e)}\n\n"
                "Backtest will continue without zones."
            )

        # Show completion message
        QMessageBox.information(
            self,
            "Backtest Started",
            f"Backtest started for {ticker} ({timeframe}).\n\n"
            "Zones have been created for each chart day index\n"
            "extending 0.9x across each day."
        )

        print(f"Backtest completed with zones for all days: {ticker} ({timeframe})")

    def create_zones_for_all_days(self):
        """Create zones per chart day index using that day's timestamp as the historical cutoff.
        For each day on the chart, compute zones with data up to that day (like volatility_statistics).
        """
        try:
            print("Creating per-day historical zones for all chart day indices...")

            # Clear existing zone graphics first (reliable, tracked list)
            if self.chart_widget and hasattr(self, '_zone_items'):
                for item in list(self._zone_items):
                    try:
                        self.chart_widget.removeItem(item)
                    except Exception:
                        pass
                self._zone_items = []
                # Also clear the separate zone type lists
                self._intraday_zone_items = []
                self._weekly_zone_items = []
                self._weekly_minavg_zone_items = []

            # Build day-index timestamps (18:00 or earliest after per day) from displayed data
            day_index_times = self._get_chart_day_index_timestamps()
            if not day_index_times:
                print("No day-index timestamps found; cannot map day indices to timestamps")
                return

            print(f"Found {len(day_index_times)} chart day-index timestamps")

            # For each chart day index, compute zones with a historical cutoff at that day's day-index timestamp
            days_with_zones = 0
            for day_index, ts in enumerate(day_index_times):
                try:
                    cutoff_idx = self._find_cutoff_index_for_timestamp(ts)
                    if cutoff_idx is None:
                        print(f"Day {day_index}: could not find cutoff index for {ts}; skipping")
                        continue

                    # Enable historical filtering for this day
                    self.viewing_historical = True
                    self.historical_timestamp = ts
                    self.historical_index = cutoff_idx
                    print(f"Day {day_index}: historical cutoff idx={cutoff_idx} for {ts}")

                    # Compute intraday zones
                    zones_result = self.calculate_zones_independently()
                    if zones_result and hasattr(zones_result, 'zones') and zones_result.zones:
                        self._add_zones_to_day(zones_result.zones, day_index)
                        days_with_zones += 1
                    else:
                        print(f"Day {day_index}: no intraday zones returned")

                    # Compute pivot zones
                    pivot_result = self.calculate_pivot_zones_independently()
                    if pivot_result and hasattr(pivot_result, 'calculation_metadata') and pivot_result.calculation_metadata:
                        metadata = pivot_result.calculation_metadata
                        optimal_zones = metadata.get('optimal_zones', [])
                        if optimal_zones:
                            self._add_pivot_zones_to_day(optimal_zones, day_index)
                    else:
                        print(f"Day {day_index}: no pivot zones returned")

                    # Defer weekly zones: we'll compute them week-by-week after the per-day loop

                except Exception as inner_e:
                    print(f"Error creating zones for day {day_index}: {inner_e}")
                    continue

            # Disable historical mode after finishing

            # Now compute weekly zones week-by-week (Weekday 1 .. Weekday 5)
            try:
                sessions = [self._session_from_timestamp(ts) for ts in day_index_times]


                i = 0
                while i < len(day_index_times):
                    if sessions[i] == '1':
                        # find end of week: first j >= i with session '5'
                        j = None
                        for k in range(i, len(day_index_times)):
                            if sessions[k] == '5':
                                j = k
                                break
                        if j is None:
                            # No Friday (5) found after this Monday; extend to end of visible chart
                            j = len(day_index_times) - 1
                        # Use the closest previous Friday (16:59) as the weekly cutoff date
                        prev_friday_ts = self._closest_previous_friday_timestamp(day_index_times[i])
                        cutoff_idx = self._find_cutoff_index_for_timestamp(prev_friday_ts)
                        if cutoff_idx is not None:
                            self.viewing_historical = True
                            self.historical_timestamp = prev_friday_ts
                            self.historical_index = cutoff_idx
                            weekly_result = self.calculate_weekly_zones_independently()
                            if weekly_result and isinstance(weekly_result, dict):
                                wz = weekly_result.get('zones', []) or []
                                if wz:
                                    # Start -1 for alignment; end -1 except for last visible week
                                    start_idx = i
                                    end_idx_for_draw = j
                                    draw_start = max(0, start_idx - 1)
                                    is_last_week = (end_idx_for_draw >= len(day_index_times) - 1)
                                    draw_end = end_idx_for_draw if is_last_week else max(draw_start, end_idx_for_draw - 1)
                                    self._add_weekly_zones_to_span(wz, draw_start, draw_end)
                        i = (j + 1)
                    else:
                        i += 1
            except Exception as e_week:
                print(f"Error computing weekly span zones: {e_week}")

            self._reset_historical_mode("per-day zones complete")
            print(f"Successfully created zones for {days_with_zones}/{len(day_index_times)} chart days")


            # If MinAvg checkbox is on, render MinAvg weekly zones daily across all days
            if hasattr(self, 'show_minavg_weekly_zones_checkbox') and self.show_minavg_weekly_zones_checkbox.isChecked():
                try:
                    self.refresh_minavg_weekly_zones()
                except Exception as e_min:
                    print(f"Error rendering daily MinAvg weekly zones: {e_min}")

        except Exception as e:
            print(f"Error creating zones for all days: {e}")
            import traceback
            traceback.print_exc()
            raise

    def _get_chart_session_end_times(self):
        """Return a list of datetime timestamps representing the end of each chart day (ETH 18:00 sessions).
        Uses the currently displayed table data (DTB-filtered) to mirror chart day indices.
        """

        try:
            if not getattr(self, 'table_widget', None) or not hasattr(self.table_widget, 'data_df'):
                return []
            df = self.table_widget.data_df
            if df is None or len(df) == 0:
                return []

            # If we have explicit Time column, build DateTime and split sessions [18:00 .. < next day's 18:00)
            if 'Date' in df.columns and 'Time' in df.columns:
                df_copy = df.copy()
                # Normalize and build DateTime
                if pd.api.types.is_datetime64_any_dtype(df_copy['Date']):
                    date_strings = df_copy['Date'].dt.strftime('%Y-%m-%d')
                else:
                    date_strings = df_copy['Date'].astype(str)

                def normalize_time(time_str):
                    time_str = str(time_str).strip()
                    if len(time_str) == 5 and time_str.count(':') == 1:
                        return time_str + ':00'
                    elif len(time_str) == 8 and time_str.count(':') == 2:
                        return time_str
                    # Try parse
                    t = pd.to_datetime(time_str, format='%H:%M:%S', errors='coerce')
                    if pd.notna(t):
                        return t.strftime('%H:%M:%S')
                    t = pd.to_datetime(time_str, format='%H:%M', errors='coerce')
                    if pd.notna(t):
                        return t.strftime('%H:%M:00')
                    return time_str

                normalized_times = df_copy['Time'].apply(normalize_time)
                df_copy['DateTime'] = pd.to_datetime(date_strings + ' ' + normalized_times, errors='coerce')
                df_copy = df_copy.dropna(subset=['DateTime']).sort_values('DateTime')
                if len(df_copy) == 0:
                    return []

                session_end_times = []
                i = 0
                eighteen = pd.Timestamp('18:00:00').time()
                while i < len(df_copy):
                    # Find first 18:00+ at or after i
                    session_start_idx = None
                    for j in range(i, len(df_copy)):
                        if df_copy.iloc[j]['DateTime'].time() >= eighteen:
                            session_start_idx = j
                            break
                    if session_start_idx is None:
                        break

                    # Find end: last row before next day's 18:00+
                    session_end_idx = len(df_copy) - 1
                    start_date = df_copy.iloc[session_start_idx]['DateTime'].date()
                    for j in range(session_start_idx + 1, len(df_copy)):
                        dtj = df_copy.iloc[j]['DateTime']
                        if dtj.date() > start_date and dtj.time() >= eighteen:
                            session_end_idx = j - 1
                            break

                    session_end_times.append(df_copy.iloc[session_end_idx]['DateTime'])
                    i = session_end_idx + 1

                return session_end_times

            # Fallback: no Time column; group by calendar Date and use last timestamp per day
            if 'Date' in df.columns:
                df_copy = df.copy()
                if not pd.api.types.is_datetime64_any_dtype(df_copy['Date']):
                    df_copy['Date'] = pd.to_datetime(df_copy['Date'], errors='coerce')
                df_copy = df_copy.dropna(subset=['Date']).sort_values('Date')
                if len(df_copy) == 0:
                    return []
                # If Date already contains time, use last Date per day; else just date at 23:59:59
                out = []
                grouped = df_copy.groupby(df_copy['Date'].dt.date)
                for _, g in grouped:
                    last_dt = g['Date'].iloc[-1]
                    if isinstance(last_dt, pd.Timestamp) and last_dt.time() != pd.Timestamp.min.time():
                        out.append(last_dt)
                    else:
                        # Compose end-of-day timestamp
                        out.append(pd.Timestamp(g['Date'].iloc[-1].date()) + pd.Timedelta(hours=23, minutes=59, seconds=59))
                return out

            return []
        except Exception as e:
            print(f"Error building chart session end times: {e}")
            return []


    def _get_chart_day_index_timestamps(self):
        """Return a list of datetime timestamps for each chart day index (the day index row itself: 18:00 or first >= 18:00).
        Strict 18:00+ only: if a day has no 18:00 or later timestamp, it is skipped.
        """
        try:
            if not getattr(self, 'table_widget', None) or not hasattr(self.table_widget, 'data_df'):
                return []
            df = self.table_widget.data_df
            if df is None or len(df) == 0:
                return []

            if 'Date' in df.columns and 'Time' in df.columns:
                df_copy = df.copy()
                # Build DateTime from Date + normalized Time
                if pd.api.types.is_datetime64_any_dtype(df_copy['Date']):
                    date_strings = df_copy['Date'].dt.strftime('%Y-%m-%d')
                else:
                    date_strings = df_copy['Date'].astype(str)

                def normalize_time(time_str):
                    time_str = str(time_str).strip()
                    if len(time_str) == 5 and time_str.count(':') == 1:
                        return time_str + ':00'
                    elif len(time_str) == 8 and time_str.count(':') == 2:
                        return time_str
                    t = pd.to_datetime(time_str, format='%H:%M:%S', errors='coerce')
                    if pd.notna(t):
                        return t.strftime('%H:%M:%S')
                    t = pd.to_datetime(time_str, format='%H:%M', errors='coerce')
                    if pd.notna(t):
                        return t.strftime('%H:%M:00')
                    return time_str

                normalized_times = df_copy['Time'].apply(normalize_time)
                df_copy['DateTime'] = pd.to_datetime(date_strings + ' ' + normalized_times, errors='coerce')
                df_copy = df_copy.dropna(subset=['DateTime']).sort_values('DateTime')
                if len(df_copy) == 0:
                    return []

                eighteen = pd.Timestamp('18:00:00').time()
                # Group by calendar date, pick earliest DateTime with time >= 18:00
                out = []
                for date_val, group in df_copy.groupby(df_copy['DateTime'].dt.date):
                    after_1800 = group[group['DateTime'].dt.time >= eighteen]
                    if not after_1800.empty:
                        # Pick earliest >= 18:00, then use the previous row in the overall sorted data as the day-index timestamp
                        first_ts = after_1800.sort_values('DateTime').iloc[0]['DateTime']
                        dti_all = pd.DatetimeIndex(df_copy['DateTime'])
                        pos = dti_all.searchsorted(first_ts, side='left') - 1
                        if pos >= 0:
                            prev_ts = df_copy.iloc[pos]['DateTime']
                            out.append(prev_ts)
                        else:
                            out.append(first_ts)
                return out

            # Strict rule: without Time, we cannot identify 18:00+ rows; skip
            return []
        except Exception as e:
            print(f"Error building chart day-index timestamps: {e}")
            return []


    def _get_total_chart_days(self):
        """Get the total number of chart days from the current chart data."""
        try:
            if not self.chart_widget:
                return 0

            # Find the maximum x-value from all chart items to determine total days
            max_x = 0
            for item in self.chart_widget.listDataItems():
                if hasattr(item, 'xData') and item.xData is not None:
                    if len(item.xData) > 0:
                        item_max = max(item.xData)
                        if item_max > max_x:
                            max_x = item_max

            # Total days is the ceiling of the highest x value
            # Since our candlesticks go from X.0 to X.9, total days is ceil(max_x)
            total_days = int(max_x) + 1 if max_x > 0 else 0

            print(f"Calculated total chart days: {total_days} (max_x: {max_x})")
            return total_days

        except Exception as e:
            print(f"Error getting total chart days: {e}")
            return 0

    def _add_zones_to_day(self, zones, day_index):
        """Add zones to a specific day index extending 0.9x across the day."""
        try:
            if not self.chart_widget or not zones:
                return

            for zone in zones:
                # Extract zone data
                if hasattr(zone, 'price'):
                    price = zone.price  # Keep original zone price values
                    level_name = getattr(zone, 'level_name', 'Unknown Zone')

                    # Determine zone color based on level name
                    zone_color = self._get_zone_color(level_name)

                    # Check if this is an extended zone with bounds
                    if hasattr(zone, 'upper_bound') and hasattr(zone, 'lower_bound'):
                        upper_bound = zone.upper_bound  # Keep original bounds
                        lower_bound = zone.lower_bound

                        # Create transparent zone area for this day
                        if zone_color.startswith('#'):
                            # Convert hex to RGB
                            hex_color = zone_color.lstrip('#')
                            r = int(hex_color[0:2], 16)
                            g = int(hex_color[2:4], 16)
                            b = int(hex_color[4:6], 16)
                            rgba_color = (r, g, b, 25)  # 25/255 = ~10% alpha
                        else:
                            # Fallback for named colors
                            rgba_color = (*pg.colorTuple(pg.mkColor(zone_color))[:3], 25)

                        # Create transparent zone area for this specific day (0.9x extension)
                        x_day = [day_index, day_index + 0.9, day_index + 0.9, day_index, day_index]
                        y_zone = [lower_bound, lower_bound, upper_bound, upper_bound, lower_bound]

                        zone_fill = pg.PlotCurveItem(
                            x_day, y_zone,
                            fillLevel=lower_bound,
                            brush=pg.mkBrush(rgba_color),
                            pen=pg.mkPen(None)  # No outline
                        )
                        # Set initial visibility based on checkbox state
                        zone_fill.setVisible(self.show_intraday_zones_checkbox.isChecked())
                        self.chart_widget.addItem(zone_fill)
                        self._zone_items.append(zone_fill)
                        self._intraday_zone_items.append(zone_fill)

                        # Add price line at original price for this day (0.9x extension)
                        price_line = pg.PlotCurveItem(
                            [day_index, day_index + 0.9],
                            [price, price],
                            pen=pg.mkPen(color=zone_color, width=2, style=Qt.PenStyle.SolidLine)
                        )
                        # Set initial visibility based on checkbox state
                        price_line.setVisible(self.show_intraday_zones_checkbox.isChecked())
                        self.chart_widget.addItem(price_line)
                        self._zone_items.append(price_line)
                        self._intraday_zone_items.append(price_line)

                    else:
                        # Regular zone without bounds - original price line for this day (0.9x extension)
                        price_line = pg.PlotCurveItem(
                            [day_index, day_index + 0.9],
                            [price, price],
                            pen=pg.mkPen(color=zone_color, width=2, style=Qt.PenStyle.SolidLine)
                        )
                        # Set initial visibility based on checkbox state
                        price_line.setVisible(self.show_intraday_zones_checkbox.isChecked())
                        self.chart_widget.addItem(price_line)
                        self._zone_items.append(price_line)
                        self._intraday_zone_items.append(price_line)

        except Exception as e:
            print(f"Error adding zones to day {day_index}: {e}")

    def _add_pivot_zones_to_day(self, optimal_zones, day_index):
        """Add pivot zones to a specific day index extending 0.9x across the day."""
        try:
            if not self.chart_widget or not optimal_zones:
                return

            # Separate zones by type (exclude midpoint 50%)
            min_avg_zones = [z for z in optimal_zones if z['type'] in ['highest_min_avg', 'lowest_min_avg']]
            range_zones = [z for z in optimal_zones if z.get('type') in ('range_25_percent', 'range_75_percent')]

            # Build pivot fills using 25%/75% and pivot low/high as requested
            if len(min_avg_zones) >= 2:
                prices = [z['price'] for z in min_avg_zones]
                highest_price = max(prices)  # Pivot high
                lowest_price = min(prices)   # Pivot low

                # Extract 25% and 75% range prices
                p25 = next((z['price'] for z in range_zones if z.get('type') == 'range_25_percent'), None)
                p75 = next((z['price'] for z in range_zones if z.get('type') == 'range_75_percent'), None)

                pivot_color = '#9c9c9c'  # Gray color as in original
                from PyQt6.QtGui import QColor

                def make_brush(alpha: int):
                    c = QColor(pivot_color)
                    c.setAlpha(alpha)
                    return pg.mkBrush(c)

                x_day = [day_index, day_index + 0.9, day_index + 0.9, day_index, day_index]

                # 25% to 75% area at 10% opacity (keep at 10%)
                if p25 is not None and p75 is not None:
                    lo = min(p25, p75)
                    hi = max(p25, p75)
                    y_zone = [lo, lo, hi, hi, lo]
                    zone_fill_mid = pg.PlotCurveItem(
                        x_day, y_zone,
                        fillLevel=lo,
                        brush=make_brush(25),  # ~10% opacity
                        pen=pg.mkPen(None)
                    )
                    # Set initial visibility based on checkbox state
                    zone_fill_mid.setVisible(self.show_intraday_zones_checkbox.isChecked())
                    self.chart_widget.addItem(zone_fill_mid)
                    self._zone_items.append(zone_fill_mid)
                    self._intraday_zone_items.append(zone_fill_mid)

                # 25% to pivot low at 20% opacity
                if p25 is not None:
                    lo = min(p25, lowest_price)
                    hi = max(p25, lowest_price)
                    y_zone = [lo, lo, hi, hi, lo]
                    zone_fill_low = pg.PlotCurveItem(
                        x_day, y_zone,
                        fillLevel=lo,
                        brush=make_brush(51),  # ~20% opacity
                        pen=pg.mkPen(None)
                    )
                    # Set initial visibility based on checkbox state
                    zone_fill_low.setVisible(self.show_intraday_zones_checkbox.isChecked())
                    self.chart_widget.addItem(zone_fill_low)
                    self._zone_items.append(zone_fill_low)
                    self._intraday_zone_items.append(zone_fill_low)

                # 75% to pivot high at 20% opacity
                if p75 is not None:
                    lo = min(p75, highest_price)
                    hi = max(p75, highest_price)
                    y_zone = [lo, lo, hi, hi, lo]
                    zone_fill_high = pg.PlotCurveItem(
                        x_day, y_zone,
                        fillLevel=lo,
                        brush=make_brush(51),  # ~20% opacity
                        pen=pg.mkPen(None)
                    )
                    # Set initial visibility based on checkbox state
                    zone_fill_high.setVisible(self.show_intraday_zones_checkbox.isChecked())
                    self.chart_widget.addItem(zone_fill_high)
                    self._zone_items.append(zone_fill_high)
                    self._intraday_zone_items.append(zone_fill_high)

            # Add solid lines for highest and lowest min avg - this day only
            for zone in min_avg_zones:
                price = zone['price']  # Keep original price

                # Create solid line spanning this day (0.9x extension) using original color
                line = pg.PlotCurveItem(
                    [day_index, day_index + 0.9],
                    [price, price],
                    pen=pg.mkPen('#9c9c9c', width=1)  # Solid gray line as in original
                )
                # Set initial visibility based on checkbox state
                line.setVisible(self.show_intraday_zones_checkbox.isChecked())
                self.chart_widget.addItem(line)
                self._zone_items.append(line)
                self._intraday_zone_items.append(line)

            # Add dashed lines for range levels (25%, 50%, 75%) - this day only
            for zone in range_zones:
                price = zone['price']  # Keep original price

                # Create dashed line spanning this day (0.9x extension) using original color
                pen = pg.mkPen('#9c9c9c', width=1, style=pg.QtCore.Qt.PenStyle.DashLine)
                line = pg.PlotCurveItem(
                    [day_index, day_index + 0.9],
                    [price, price],
                    pen=pen
                )
                # Set initial visibility based on checkbox state
                line.setVisible(self.show_intraday_zones_checkbox.isChecked())
                self.chart_widget.addItem(line)
                self._zone_items.append(line)
                self._intraday_zone_items.append(line)

        except Exception as e:
            print(f"Error adding pivot zones to day {day_index}: {e}")


    def _reset_historical_mode(self, reason: str = ""):
        """Clear historical filtering state so future calcs use full/latest data."""
        try:
            if getattr(self, 'viewing_historical', False) or getattr(self, 'historical_index', None) is not None:
                msg = f"Resetting historical mode" + (f" ({reason})" if reason else "")
                print(msg)
            self.viewing_historical = False
            self.historical_timestamp = None
            self.historical_index = None
        except Exception:
            # Fail-safe: ensure flags are cleared
            self.viewing_historical = False
            self.historical_timestamp = None
            self.historical_index = None

    def on_data_received(self, data):
        """
        Receive data from Universal Controls - required for centralized data system.
        This method is automatically called when Universal Controls fetches new data.
        """
        try:
            print("BacktesterTab: Received data from Universal Controls")
            self._reset_historical_mode("new centralized market data received")

            # Store the universal controls data
            self.universal_controls_data = data

            # Log data info
            if isinstance(data, dict):
                ticker = data.get("ticker", "Unknown")
                timeframe = data.get("timeframe", "Unknown")
                print(f"BacktesterTab: Data received for {ticker} ({timeframe})")

            # Note: We don't automatically update charts here since user might have CSV data loaded
            # The universal controls data is stored separately and used for backtesting

        except Exception as e:
            print(f"BacktesterTab: Error receiving universal controls data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to receive data from Universal Controls: {str(e)}")

    def calculate_and_display_zones(self):
        """Calculate intraday zones independently and display them on the chart."""
        try:
            print("BacktesterTab: Calculating intraday zones independently...")

            # Use independent zone calculation
            zones_result = self.calculate_zones_independently()

            if not zones_result:
                raise Exception("Failed to calculate zones independently")

            # Extract zones from the calculation result
            if hasattr(zones_result, 'zones') and zones_result.zones:
                self.add_zones_to_chart(zones_result.zones)

                zone_count = len(zones_result.zones)
                print(f"BacktesterTab: Independently calculated and displayed {zone_count} intraday zones")
            else:
                raise Exception("No zone data found after independent calculation")

        except Exception as e:
            print(f"BacktesterTab: Error calculating/displaying intraday zones independently: {e}")
            raise

    def calculate_and_display_pivot_zones(self):
        """Calculate pivot zones independently and display them on the chart."""
        try:
            print("BacktesterTab: Calculating pivot zones independently...")

            # Use independent pivot zone calculation
            pivot_zones_result = self.calculate_pivot_zones_independently()

            if not pivot_zones_result:
                raise Exception("Failed to calculate pivot zones independently")

            # Extract optimal zones from the calculation result
            if hasattr(pivot_zones_result, 'calculation_metadata') and pivot_zones_result.calculation_metadata:
                metadata = pivot_zones_result.calculation_metadata
                optimal_zones = metadata.get('optimal_zones', [])

                if optimal_zones:
                    self.add_pivot_zones_to_chart(optimal_zones)
                    print(f"BacktesterTab: Independently calculated and displayed {len(optimal_zones)} pivot zones")
                else:
                    print("BacktesterTab: No optimal zones found in independent pivot data")
            else:
                raise Exception("No pivot zone metadata found after independent calculation")

        except Exception as e:
            print(f"BacktesterTab: Error calculating/displaying pivot zones independently: {e}")
            import traceback
            traceback.print_exc()
            raise

    def add_zones_to_chart(self, zones):
        """Add zone price levels and transparent ranges to the chart - latest day only."""
        try:
            if not self.chart_widget or not zones:
                return

            print(f"Adding {len(zones)} zones to chart...")

            # Get the latest day index from current chart data
            latest_day_index = self._get_latest_day_index()
            if latest_day_index is None:
                print("No chart data available to determine latest day")
                return

            print(f"Displaying zones on latest day (index {latest_day_index})")

            for zone in zones:
                # Extract zone data
                if hasattr(zone, 'price'):
                    price = zone.price
                    level_name = getattr(zone, 'level_name', 'Unknown Zone')

                    # Determine zone color based on level name
                    zone_color = self._get_zone_color(level_name)

                    # Check if this is an extended zone with bounds
                    if hasattr(zone, 'upper_bound') and hasattr(zone, 'lower_bound'):
                        upper_bound = zone.upper_bound
                        lower_bound = zone.lower_bound

                        # Create transparent zone area using LinearRegionItem
                        # Convert hex color to RGB for proper transparency
                        if zone_color.startswith('#'):
                            # Convert hex to RGB
                            hex_color = zone_color.lstrip('#')
                            r = int(hex_color[0:2], 16)
                            g = int(hex_color[2:4], 16)
                            b = int(hex_color[4:6], 16)
                            rgba_color = (r, g, b, 25)  # 25/255 = ~10% alpha
                        else:
                            # Fallback for named colors
                            rgba_color = (*pg.colorTuple(pg.mkColor(zone_color))[:3], 25)

                        # Create transparent zone area for latest day only using PlotCurveItem
                        x_latest_day = [latest_day_index, latest_day_index + 0.9, latest_day_index + 0.9, latest_day_index, latest_day_index]
                        y_zone = [lower_bound, lower_bound, upper_bound, upper_bound, lower_bound]

                        zone_fill = pg.PlotCurveItem(
                            x_latest_day, y_zone,
                            fillLevel=lower_bound,
                            brush=pg.mkBrush(rgba_color),
                            pen=pg.mkPen(None)  # No outline
                        )
                        # Set initial visibility based on checkbox state
                        zone_fill.setVisible(self.show_intraday_zones_checkbox.isChecked())
                        self.chart_widget.addItem(zone_fill)
                        self._zone_items.append(zone_fill)
                        self._intraday_zone_items.append(zone_fill)

                        # Add center price line for latest day only (NO LABEL)
                        price_line = pg.PlotCurveItem(
                            [latest_day_index, latest_day_index + 0.9],  # Latest day span
                            [price, price],  # Horizontal line at price level
                            pen=pg.mkPen(color=zone_color, width=2, style=Qt.PenStyle.SolidLine)
                        )
                        # Set initial visibility based on checkbox state
                        price_line.setVisible(self.show_intraday_zones_checkbox.isChecked())
                        self.chart_widget.addItem(price_line)
                        self._zone_items.append(price_line)
                        self._intraday_zone_items.append(price_line)

                        print(f"Added zone: {level_name} at ${price:.2f} [{lower_bound:.2f} - {upper_bound:.2f}]")

                    else:
                        # Regular zone without bounds - price line for latest day only (NO LABEL)
                        price_line = pg.PlotCurveItem(
                            [latest_day_index, latest_day_index + 0.9],  # Latest day span
                            [price, price],  # Horizontal line at price level
                            pen=pg.mkPen(color=zone_color, width=2, style=Qt.PenStyle.SolidLine)
                        )
                        # Set initial visibility based on checkbox state
                        price_line.setVisible(self.show_intraday_zones_checkbox.isChecked())
                        self.chart_widget.addItem(price_line)
                        self._zone_items.append(price_line)
                        self._intraday_zone_items.append(price_line)

                        print(f"Added zone: {level_name} at ${price:.2f}")

            print(f"Successfully added {len(zones)} zones to chart")

        except Exception as e:
            print(f"Error adding zones to chart: {e}")
            QMessageBox.critical(self, "Error", f"Failed to add zones to chart: {str(e)}")

    def add_pivot_zones_to_chart(self, optimal_zones):
        """Add pivot zone price levels and transparent ranges to the chart - latest day only."""
        try:
            if not self.chart_widget or not optimal_zones:
                return

            print(f"Adding {len(optimal_zones)} pivot zones to chart...")

            # Get the latest day index from current chart data
            latest_day_index = self._get_latest_day_index()
            if latest_day_index is None:
                print("No chart data available to determine latest day")
                return

            # Separate zones by type (exclude midpoint 50%)
            min_avg_zones = [z for z in optimal_zones if z['type'] in ['highest_min_avg', 'lowest_min_avg']]
            range_zones = [z for z in optimal_zones if z.get('type') in ('range_25_percent', 'range_75_percent')]

            # Get highest and lowest min avg prices for zone area
            if len(min_avg_zones) >= 2:
                prices = [z['price'] for z in min_avg_zones]
                highest_price = max(prices)
                lowest_price = min(prices)

                # Create transparent zone area from highest to lowest min avg (latest day only)
                pivot_color = '#9c9c9c'  # Gray color as specified

                # Convert hex to RGB and create brush with 10% transparency
                from PyQt6.QtGui import QColor
                color = QColor(pivot_color)
                color.setAlpha(25)  # 10% transparency (25/255 ≈ 0.1)

                x_latest_day = [latest_day_index, latest_day_index + 0.9, latest_day_index + 0.9, latest_day_index, latest_day_index]
                y_zone = [lowest_price, lowest_price, highest_price, highest_price, lowest_price]

                zone_fill = pg.PlotCurveItem(
                    x_latest_day, y_zone,
                    fillLevel=lowest_price,
                    brush=pg.mkBrush(color),
                    pen=pg.mkPen(None)  # No outline
                )
                # Set initial visibility based on checkbox state
                zone_fill.setVisible(self.show_intraday_zones_checkbox.isChecked())
                self.chart_widget.addItem(zone_fill)
                self._zone_items.append(zone_fill)
                self._intraday_zone_items.append(zone_fill)
                print(f"Added pivot zone area from ${lowest_price:.2f} to ${highest_price:.2f}")

            # Add solid lines for highest and lowest min avg (latest day only)
            for zone in min_avg_zones:
                price = zone['price']
                zone_type = zone['type']

                # Create solid line spanning latest day
                line = pg.PlotCurveItem(
                    [latest_day_index, latest_day_index + 0.9],
                    [price, price],
                    pen=pg.mkPen('#9c9c9c', width=1)  # Solid gray line
                )
                # Set initial visibility based on checkbox state
                line.setVisible(self.show_intraday_zones_checkbox.isChecked())
                self.chart_widget.addItem(line)
                self._zone_items.append(line)
                self._intraday_zone_items.append(line)
                print(f"Added solid pivot line: {zone_type} at ${price:.2f}")

            # Add dashed lines for range levels (25%, 50%, 75%) - latest day only
            for zone in range_zones:
                price = zone['price']
                zone_type = zone['type']

                # Create dashed line spanning latest day
                pen = pg.mkPen('#9c9c9c', width=1, style=pg.QtCore.Qt.PenStyle.DashLine)
                line = pg.PlotCurveItem(
                    [latest_day_index, latest_day_index + 0.9],
                    [price, price],
                    pen=pen
                )
                # Set initial visibility based on checkbox state
                line.setVisible(self.show_intraday_zones_checkbox.isChecked())
                self.chart_widget.addItem(line)
                self._zone_items.append(line)
                self._intraday_zone_items.append(line)

                # Get percentage label (50% removed)
                if zone_type == 'range_25_percent':
                    label = '25%'
                elif zone_type == 'range_75_percent':
                    label = '75%'
                else:
                    label = zone_type

                print(f"Added dashed pivot line: {label} at ${price:.2f}")

        except Exception as e:
            print(f"Error adding pivot zones to chart: {e}")
            import traceback
            traceback.print_exc()

    def _get_latest_day_index(self):
        """Get the actual latest day index from the candlestick chart data."""
        try:
            # Get the chart widget's plot items to find the actual candlestick data
            if not self.chart_widget:
                return None

            # Look for the last candlestick data that was added to the chart
            # We need to find the highest x-value (day index) from our candlestick data
            max_x = 0

            # Check all plot items in the chart
            for item in self.chart_widget.listDataItems():
                if hasattr(item, 'xData') and item.xData is not None:
                    if len(item.xData) > 0:
                        item_max = max(item.xData)
                        if item_max > max_x:
                            max_x = item_max

            # The latest day index is the floor of the highest x value
            # Since our candlesticks go from X.0 to X.9, the day index is floor(max_x)
            latest_day_index = int(max_x)

            print(f"Found latest day index from chart data: {latest_day_index}")
            return latest_day_index

        except Exception as e:
            print(f"Error getting latest day index from chart: {e}")
            return 0  # Default to day 0 if error

    def _get_zone_color(self, level_name):
        """Get the appropriate color for a zone based on its level name."""
        level_name_lower = level_name.lower()

        if 'maxavg' in level_name_lower or 'max_avg' in level_name_lower:
            return '#00bcd4'  # MaxAvg color
        elif 'median' in level_name_lower:
            return '#e65100'  # Median color
        elif 'avg' in level_name_lower or 'average' in level_name_lower:
            return '#ffffff'  # Average color
        else:
            # Default colors for unknown zone types
            return '#888888'  # Gray for unknown

    def _get_weekly_zone_color(self, stat_name):
        """Get the appropriate color for weekly zones based on stat name."""
        stat_lower = stat_name.lower()

        if 'min_avg' in stat_lower:
            return '#ffeb3b'  # MinAvg yellow
        elif 'median' in stat_lower:
            return '#e65100'  # Median orange
        elif 'average' in stat_lower:
            return '#808080'  # Average gray
        elif 'max_avg' in stat_lower:
            return '#00bcd4'  # MaxAvg cyan
        else:
            return '#888888'  # Default gray

    def update_chart_from_dtb(self):
        """Update chart with ASCENDING chronological order - earliest date at index 0, latest at last index."""
        if self.current_data is None or len(self.current_data) == 0:
            return

        try:
            # Get DTB value
            days_to_back = self.dtb_input.value()

            # Check if we have Date column for proper date filtering
            if 'Date' in self.current_data.columns:
                # Convert Date column to datetime if it's not already
                df_copy = self.current_data.copy()

                # Check for any NaT values before processing and fill them
                initial_nat_count = df_copy['Date'].isnull().sum()
                if initial_nat_count > 0:
                    df_copy['Date'] = df_copy['Date'].ffill().bfill()

                df_copy['Date'] = pd.to_datetime(df_copy['Date'], errors='coerce')

                # If we have Time column, create a proper DateTime column for sorting
                if 'Time' in df_copy.columns:
                    # Combine Date and Time for proper chronological sorting
                    try:
                        # Convert Time to string if it's not already
                        df_copy['Time'] = df_copy['Time'].astype(str)
                        # Create full datetime by combining Date and Time
                        df_copy['FullDateTime'] = pd.to_datetime(
                            df_copy['Date'].dt.strftime('%Y-%m-%d') + ' ' + df_copy['Time'],
                            errors='coerce'
                        )
                        # Sort by full datetime for proper chronological order
                        df_copy = df_copy.sort_values('FullDateTime')
                        print(f"Sorting by FullDateTime. Sample: {df_copy['FullDateTime'].head(3).tolist()}")
                    except Exception as e:
                        print(f"Warning: Could not create FullDateTime, sorting by Date only: {e}")
                        df_copy = df_copy.sort_values('Date')
                else:
                    # Sort by date only if no time column
                    df_copy = df_copy.sort_values('Date')

                # Get unique dates and take the last N unique dates
                unique_dates = df_copy['Date'].dt.date.unique()
                if len(unique_dates) < days_to_back:
                    selected_dates = unique_dates
                else:
                    # Take the LAST N unique dates (most recent)
                    selected_dates = unique_dates[-days_to_back:]

                # Filter data to only include the selected dates
                filtered_data = df_copy[df_copy['Date'].dt.date.isin(selected_dates)]

                # Keep ASCENDING chronological order (earliest datetime first, latest last)
                if 'FullDateTime' in filtered_data.columns:
                    filtered_data = filtered_data.sort_values('FullDateTime', ascending=True)
                    # Drop the temporary FullDateTime column
                    filtered_data = filtered_data.drop('FullDateTime', axis=1)
                else:
                    filtered_data = filtered_data.sort_values('Date', ascending=True)

                if len(filtered_data) == 0:
                    QMessageBox.warning(self, "No Data", f"No data found for the last {days_to_back} days")
                    return

            else:
                # If no Date column, take the last N rows in chronological order
                # Calculate rows per day (rough estimate: 1440 minutes per day for minute data)
                estimated_rows_per_day = 1440  # Assume minute data
                rows_to_take = min(days_to_back * estimated_rows_per_day, len(self.current_data))

                # Take the LAST rows (most recent data) and keep chronological order
                filtered_data = self.current_data.tail(rows_to_take)

            print(f"=== DTB FILTERING DEBUG ===")
            print(f"DTB days requested: {days_to_back}")
            print(f"Filtered data shape: {filtered_data.shape}")
            if 'Date' in filtered_data.columns:
                print(f"Date range: {filtered_data['Date'].min()} to {filtered_data['Date'].max()}")
                print(f"First date (index 0): {filtered_data['Date'].iloc[0]}")
                print(f"Last date (last index): {filtered_data['Date'].iloc[-1]}")
            print("===========================")

            # Store filtered data for crosshair mapping
            self.filtered_chart_data = filtered_data
            
            # Update the chart with filtered data (ASCENDING chronological order)
            self.update_candlestick_chart(filtered_data)

            # Update the table with the same filtered data used in the chart
            self.update_table_with_filtered_data(filtered_data)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to filter data: {str(e)}")

    def calculate_atr(self, df, period=14):
        """
        Calculate Average True Range (ATR) for the given dataframe.

        ATR measures volatility by calculating the average of true ranges over a specified period.
        True Range is the maximum of:
        1. Current High - Current Low
        2. Absolute value of (Current High - Previous Close)
        3. Absolute value of (Current Low - Previous Close)

        Args:
            df: DataFrame with OHLC data
            period: Number of periods for ATR calculation (default: 14)

        Returns:
            DataFrame with ATR column added
        """
        try:
            # Make a copy to avoid modifying the original
            df_copy = df.copy()

            # Determine which column to use for Close price
            close_col = None
            if 'Close' in df_copy.columns:
                close_col = 'Close'
            elif 'Last' in df_copy.columns:
                close_col = 'Last'
            else:
                print("Warning: No Close or Last column found for ATR calculation")
                df_copy['ATR'] = 0.0
                return df_copy

            # Check if we have required columns
            required_cols = ['High', 'Low', close_col]
            missing_cols = [col for col in required_cols if col not in df_copy.columns]
            if missing_cols:
                print(f"Warning: Missing columns for ATR calculation: {missing_cols}")
                df_copy['ATR'] = 0.0
                return df_copy

            # Ensure numeric data types
            for col in required_cols:
                df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')

            # Calculate True Range components
            # 1. High - Low
            hl = df_copy['High'] - df_copy['Low']

            # 2. |High - Previous Close|
            hc = abs(df_copy['High'] - df_copy[close_col].shift(1))

            # 3. |Low - Previous Close|
            lc = abs(df_copy['Low'] - df_copy[close_col].shift(1))

            # True Range is the maximum of the three
            true_range = pd.concat([hl, hc, lc], axis=1).max(axis=1)

            # Calculate ATR as the rolling average of True Range
            atr = true_range.rolling(window=period, min_periods=1).mean()

            # Add ATR column to dataframe
            df_copy['ATR'] = atr

            print(f"ATR calculated successfully with period={period}")
            return df_copy

        except Exception as e:
            print(f"Error calculating ATR: {e}")
            import traceback
            traceback.print_exc()
            # Return original dataframe with ATR column set to 0
            df['ATR'] = 0.0
            return df

    def calculate_weekday(self, df):
        """
        Calculate weekday for each row based on the Date column.

        Args:
            df: DataFrame with Date column

        Returns:
            DataFrame with Weekday column added
        """
        try:
            # Make a copy to avoid modifying the original
            df_copy = df.copy()

            # Check if Date column exists
            if 'Date' not in df_copy.columns:
                print("Warning: No Date column found for weekday calculation")
                df_copy['Weekday'] = 'Unknown'
                return df_copy

            # Ensure Date column is datetime
            if not pd.api.types.is_datetime64_any_dtype(df_copy['Date']):
                try:
                    df_copy['Date'] = pd.to_datetime(df_copy['Date'], errors='coerce')
                except Exception as e:
                    print(f"Warning: Could not convert Date column to datetime: {e}")
                    df_copy['Weekday'] = 'Unknown'
                    return df_copy

            # Calculate custom weekday mapping based on trading sessions
            # 1 = Sunday 18:00 → Monday 16:59
            # 2 = Monday 18:00 → Tuesday 16:59
            # 3 = Tuesday 18:00 → Wednesday 16:59
            # 4 = Wednesday 18:00 → Thursday 16:59
            # 5 = Thursday 18:00 → Friday 16:59
            def get_trading_weekday(row):
                try:
                    date = row['Date']
                    time_str = row.get('Time', '16:00:00')  # Default to 16:00 for daily data

                    if pd.isna(date):
                        return 'Unknown'

                    # Create full datetime by combining date and time
                    if 'Time' in row and pd.notna(row['Time']):
                        # Intraday data - use actual time
                        try:
                            # Normalize time string
                            time_str = str(time_str).strip()
                            if len(time_str) == 5 and time_str.count(':') == 1:
                                time_str = time_str + ':00'

                            # Combine date and time
                            if hasattr(date, 'date'):
                                date_part = date.date()
                            else:
                                date_part = pd.to_datetime(date).date()

                            full_datetime = pd.to_datetime(f"{date_part} {time_str}")
                        except:
                            # Fallback to date with default time
                            full_datetime = pd.to_datetime(date)
                    else:
                        # Daily data - treat as 16:00 (market close)
                        if hasattr(date, 'date'):
                            date_part = date.date()
                        else:
                            date_part = pd.to_datetime(date).date()
                        full_datetime = pd.to_datetime(f"{date_part} 16:00:00")

                    # Get day of week (0=Monday, 6=Sunday)
                    dow = full_datetime.weekday()
                    time_obj = full_datetime.time()

                    # Define session boundary
                    session_start = pd.Timestamp('18:00:00').time()  # 18:00

                    # Determine which trading session this timestamp belongs to
                    if time_obj >= session_start:
                        # After 18:00 - belongs to next day's session
                        if dow == 6:  # Sunday 18:00+ -> Session 1 (Monday)
                            return '1'
                        elif dow == 0:  # Monday 18:00+ -> Session 2 (Tuesday)
                            return '2'
                        elif dow == 1:  # Tuesday 18:00+ -> Session 3 (Wednesday)
                            return '3'
                        elif dow == 2:  # Wednesday 18:00+ -> Session 4 (Thursday)
                            return '4'
                        elif dow == 3:  # Thursday 18:00+ -> Session 5 (Friday)
                            return '5'
                        else:  # Friday/Saturday 18:00+ -> Weekend
                            return 'Unknown'
                    else:
                        # Before 18:00 - belongs to current day's session
                        if dow == 0:  # Monday before 18:00 -> Session 1
                            return '1'
                        elif dow == 1:  # Tuesday before 18:00 -> Session 2
                            return '2'
                        elif dow == 2:  # Wednesday before 18:00 -> Session 3
                            return '3'
                        elif dow == 3:  # Thursday before 18:00 -> Session 4
                            return '4'
                        elif dow == 4:  # Friday before 18:00 -> Session 5
                            return '5'
                        else:  # Saturday/Sunday before 18:00 -> Weekend
                            return 'Unknown'

                except Exception as e:
                    print(f"Error calculating trading weekday: {e}")
                    return 'Unknown'

            df_copy['Weekday'] = df_copy.apply(get_trading_weekday, axis=1)

            # Handle any remaining unknown values
            df_copy['Weekday'] = df_copy['Weekday'].fillna('Unknown')

            print("Weekday calculated successfully")
            return df_copy

        except Exception as e:
            print(f"Error calculating weekday: {e}")
            import traceback
            traceback.print_exc()
            # Return original dataframe with Weekday column set to 'Unknown'
            df['Weekday'] = 'Unknown'
            return df

    def update_table_with_filtered_data(self, filtered_data):
        """Update table widget with the same filtered data used in the chart."""
        try:
            if not self.table_widget or filtered_data is None or len(filtered_data) == 0:
                return

            # Calculate ATR and Weekday before filtering columns
            filtered_data_with_atr = self.calculate_atr(filtered_data.copy())
            filtered_data_with_calculations = self.calculate_weekday(filtered_data_with_atr)

            # Define required columns in the desired order
            required_columns = ['Date', 'Time', 'Weekday', 'Open', 'High', 'Low', 'Close', 'Volume', 'ATR']

            # Filter data to only include required columns that exist
            available_columns = []
            for col in required_columns:
                if col in filtered_data_with_calculations.columns:
                    available_columns.append(col)
                elif col == 'Close' and 'Last' in filtered_data_with_calculations.columns:
                    # Map 'Last' to 'Close' if Close doesn't exist
                    available_columns.append('Last')
                else:
                    print(f"Warning: Required column '{col}' not found in data")

            # Create a copy with only the required columns
            if available_columns:
                formatted_df = filtered_data_with_calculations[available_columns].copy()
                # Rename 'Last' to 'Close' if we used it
                if 'Last' in formatted_df.columns and 'Close' not in formatted_df.columns:
                    formatted_df = formatted_df.rename(columns={'Last': 'Close'})
            else:
                print("Error: No required columns found in data")
                return

            # Handle Date and Time columns specially
            if 'Date' in formatted_df.columns:
                # Check for NaT values before formatting and fill them
                nat_count = formatted_df['Date'].isnull().sum()
                if nat_count > 0:
                    formatted_df['Date'] = formatted_df['Date'].ffill().bfill()

                # If Date is already datetime, convert to proper string format
                if pd.api.types.is_datetime64_any_dtype(formatted_df['Date']):
                    # Handle NaT values properly before strftime
                    def format_date_safe(date_val):
                        if pd.isna(date_val):
                            return ""  # Return empty string for NaT/NaN values
                        try:
                            return date_val.strftime('%Y-%m-%d')
                        except:
                            return str(date_val)  # Fallback to string conversion

                    formatted_df['Date'] = formatted_df['Date'].apply(format_date_safe)
                # If Date is string but looks like datetime, try to parse and format
                elif formatted_df['Date'].dtype == 'object':
                    try:
                        # Try to parse as datetime and reformat
                        temp_dates = pd.to_datetime(formatted_df['Date'], errors='coerce')
                        if not temp_dates.isna().all():
                            # Use the same safe formatting function
                            def format_date_safe(date_val):
                                if pd.isna(date_val):
                                    return ""  # Return empty string for NaT/NaN values
                                try:
                                    return date_val.strftime('%Y-%m-%d')
                                except:
                                    return str(date_val)  # Fallback to string conversion

                            formatted_df['Date'] = temp_dates.apply(format_date_safe)
                    except:
                        pass  # Keep original format if parsing fails

            if 'Time' in formatted_df.columns:
                # Ensure Time column is properly formatted and consistent
                formatted_df['Time'] = formatted_df['Time'].astype(str)
                # Ensure consistent HH:MM:SS format
                def format_time(time_str):
                    try:
                        # Handle various time formats
                        time_str = str(time_str).strip()
                        if len(time_str) == 8 and time_str.count(':') == 2:
                            # Already in HH:MM:SS format
                            return time_str
                        elif len(time_str) == 5 and time_str.count(':') == 1:
                            # HH:MM format, add seconds
                            return time_str + ':00'
                        else:
                            # Try to parse and reformat
                            parsed_time = pd.to_datetime(time_str, format='%H:%M:%S', errors='coerce')
                            if pd.notna(parsed_time):
                                return parsed_time.strftime('%H:%M:%S')
                            else:
                                return time_str  # Return original if can't parse
                    except:
                        return str(time_str)

                formatted_df['Time'] = formatted_df['Time'].apply(format_time)

            # Apply formatting to numeric columns
            for col in formatted_df.columns:
                if col not in ['Date', 'Time', 'Weekday'] and formatted_df[col].dtype in ['float64', 'float32', 'int64', 'int32']:
                    # Convert to string with full precision, no scientific notation
                    if formatted_df[col].dtype in ['float64', 'float32']:
                        # For float columns, format with appropriate decimal places
                        formatted_df[col] = formatted_df[col].apply(lambda x: f"{x:.2f}" if pd.notna(x) else "")
                    else:
                        # For integer columns, show as integers
                        formatted_df[col] = formatted_df[col].apply(lambda x: f"{int(x)}" if pd.notna(x) else "")

            # Convert to numpy array for table display
            data_array = formatted_df.values
            self.table_widget.setData(data_array, filtered_data)

            # Set the correct column headers (ensure they match the required columns)
            column_headers = formatted_df.columns.tolist()
            self.table_widget.setHorizontalHeaderLabels(column_headers)

            print(f"Table updated with {len(filtered_data):,} rows and {len(column_headers)} columns: {column_headers}")
            print(f"Date column dtype: {filtered_data['Date'].dtype if 'Date' in filtered_data.columns else 'N/A'}")
            print(f"First few dates: {filtered_data['Date'].head(3).tolist() if 'Date' in filtered_data.columns else 'N/A'}")

        except Exception as e:
            print(f"Error updating table: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_candlestick_chart(self, df):
        """Create EXACT candlestick chart - no phantom data."""
        try:
            if df is None or len(df) == 0:
                return

            # Clear existing chart
            self.chart_widget.clear()
            
            # Reset and re-setup crosshair after clearing
            self.reset_crosshair()
            self.setup_crosshair()

            # Check if we have the required OHLC columns
            required_cols = ['Open', 'High', 'Low', 'Last']
            if not all(col in df.columns for col in required_cols):
                QMessageBox.warning(self, "Chart Error", "CSV must contain Open, High, Low, Last columns")
                return

            print("=== RAW CSV DATA DEBUG ===")
            print(f"Input DataFrame shape: {df.shape}")
            print(f"Date column dtype: {df['Date'].dtype if 'Date' in df.columns else 'N/A'}")
            print(f"Time column dtype: {df['Time'].dtype if 'Time' in df.columns else 'N/A'}")

            if 'Date' in df.columns:
                print(f"Sample dates: {df['Date'].head(3).tolist()}")
            if 'Time' in df.columns:
                print(f"Sample times: {df['Time'].head(3).tolist()}")

            print("First 3 rows of raw data:")
            for i in range(min(3, len(df))):
                row = df.iloc[i]
                print(f"Row {i}: Open={row['Open']}, High={row['High']}, Low={row['Low']}, Last={row['Last']}")

            # Check if we have Time column for 18:00 positioning
            has_time = 'Time' in df.columns

            # DIRECT mapping with 18:00-based positioning
            candlestick_data = []

            if has_time:
                # ETH to RTH logic: 18:00 previous day (included) to 18:00 current day (excluded)
                df_copy = df.copy()
                if 'Date' in df.columns:
                    # Handle datetime combination properly - Date is already datetime, Time is string
                    try:
                        # If Date is datetime, convert to date string first
                        if pd.api.types.is_datetime64_any_dtype(df_copy['Date']):
                            date_strings = df_copy['Date'].dt.strftime('%Y-%m-%d')
                        else:
                            date_strings = df_copy['Date'].astype(str)

                        # Normalize time format before combining
                        def normalize_time(time_str):
                            time_str = str(time_str).strip()
                            if len(time_str) == 5 and time_str.count(':') == 1:
                                # HH:MM format, add seconds
                                return time_str + ':00'
                            elif len(time_str) == 8 and time_str.count(':') == 2:
                                # Already HH:MM:SS format
                                return time_str
                            else:
                                # Try to parse and reformat
                                try:
                                    parsed_time = pd.to_datetime(time_str, format='%H:%M:%S', errors='coerce')
                                    if pd.notna(parsed_time):
                                        return parsed_time.strftime('%H:%M:%S')
                                    else:
                                        # Try HH:MM format
                                        parsed_time = pd.to_datetime(time_str, format='%H:%M', errors='coerce')
                                        if pd.notna(parsed_time):
                                            return parsed_time.strftime('%H:%M:00')
                                        else:
                                            return time_str  # Return original if can't parse
                                except:
                                    return time_str

                        normalized_times = df_copy['Time'].apply(normalize_time)

                        # Combine date and time strings, then convert to datetime
                        df_copy['DateTime'] = pd.to_datetime(
                            date_strings + ' ' + normalized_times,
                            errors='coerce'
                        )

                        # Remove any rows with NaT (invalid datetime)
                        df_copy = df_copy.dropna(subset=['DateTime'])

                        if len(df_copy) == 0:
                            QMessageBox.warning(self, "Chart Error", "No valid datetime data found")
                            return

                        df_copy = df_copy.sort_values('DateTime')
                        print(f"Created {len(df_copy)} valid datetime entries for chart")

                    except Exception as e:
                        print(f"Error creating DateTime column: {e}")
                        QMessageBox.critical(self, "Chart Error", f"Failed to process datetime data: {str(e)}")
                        return

                    # Group data by ETH-RTH sessions (18:00 to 18:00)
                    current_session_index = 0
                    i = 0

                    while i < len(df_copy):
                        # Find session start: earliest row >= 18:00:00 (ETH start)
                        session_start_idx = None
                        for j in range(i, len(df_copy)):
                            row_time = df_copy.iloc[j]['DateTime'].time()
                            if row_time >= pd.Timestamp('18:00:00').time():
                                session_start_idx = j
                                break

                        if session_start_idx is None:
                            break  # No more 18:00+ times found

                        # Find session end: latest row < 18:00:00 next day (RTH end)
                        session_end_idx = None
                        session_start_date = df_copy.iloc[session_start_idx]['DateTime'].date()

                        for j in range(session_start_idx + 1, len(df_copy)):
                            row_time = df_copy.iloc[j]['DateTime'].time()
                            row_date = df_copy.iloc[j]['DateTime'].date()

                            # If we hit 18:00+ on a different date, stop before it
                            if row_date > session_start_date and row_time >= pd.Timestamp('18:00:00').time():
                                session_end_idx = j - 1
                                break

                        if session_end_idx is None:
                            session_end_idx = len(df_copy) - 1  # Use last row if no next 18:00 found

                        # Process this ETH-RTH session
                        session_data = df_copy.iloc[session_start_idx:session_end_idx + 1]
                        num_candles_in_session = len(session_data)

                        for k, (_, row) in enumerate(session_data.iterrows()):
                            try:
                                o = float(row['Open'])
                                h = float(row['High'])
                                l = float(row['Low'])
                                c = float(row['Last'])

                                # Position: session_index + (candle_position_in_session / num_candles_in_session) * 0.9
                                if num_candles_in_session > 1:
                                    x_pos = current_session_index + (k / (num_candles_in_session - 1)) * 0.9
                                else:
                                    x_pos = current_session_index + 0.45  # Center of the session

                                candlestick_data.append((x_pos, o, h, l, c))

                                if len(candlestick_data) <= 3:
                                    time_str = row['Time'] if 'Time' in row else 'N/A'
                                    date_str = row['Date'] if 'Date' in row else 'N/A'
                                    print(f"Candlestick {len(candlestick_data)-1}: x={x_pos:.2f}, Date={date_str}, Time={time_str}, O={o}, H={h}, L={l}, C={c}")

                            except (ValueError, TypeError) as e:
                                print(f"Skipping row due to conversion error: {e}")
                                continue

                        current_session_index += 1
                        i = session_end_idx + 1  # Move to next potential session start
                else:
                    # No date column, treat as single day
                    for i in range(len(df)):
                        try:
                            row = df.iloc[i]
                            o = float(row['Open'])
                            h = float(row['High'])
                            l = float(row['Low'])
                            c = float(row['Last'])

                            # Position within single day (0.0 to 0.9)
                            if len(df) > 1:
                                x_pos = (i / (len(df) - 1)) * 0.9
                            else:
                                x_pos = 0.45

                            candlestick_data.append((x_pos, o, h, l, c))

                            if i < 3:
                                time_str = row['Time'] if 'Time' in row else 'N/A'
                                print(f"Candlestick {i}: x={x_pos:.2f}, Time={time_str}, O={o}, H={h}, L={l}, C={c}")

                        except (ValueError, TypeError) as e:
                            print(f"Skipping row {i} due to conversion error: {e}")
                            continue
            else:
                # No time column, use simple sequential positioning
                for i in range(len(df)):
                    try:
                        row = df.iloc[i]
                        o = float(row['Open'])
                        h = float(row['High'])
                        l = float(row['Low'])
                        c = float(row['Last'])

                        # Simple sequential positioning
                        x_pos = i
                        candlestick_data.append((x_pos, o, h, l, c))

                        if i < 3:
                            print(f"Candlestick {i}: x={x_pos}, O={o}, H={h}, L={l}, C={c}")

                    except (ValueError, TypeError) as e:
                        print(f"Skipping row {i} due to conversion error: {e}")
                        continue

            print(f"Total candlesticks created: {len(candlestick_data)}")
            print("========================")

            if len(candlestick_data) == 0:
                QMessageBox.warning(self, "Chart Error", "No valid candlestick data")
                return

            # Store candlestick data for crosshair mapping
            self.candlestick_data = candlestick_data
            
            # Create simple candlestick visualization
            self.create_simple_candlesticks(candlestick_data)

            # Set chart title
            dtb_days = self.dtb_input.value() if self.dtb_input else 30
            self.chart_widget.setTitle(f"Candlestick Chart - Last {dtb_days} days ({len(candlestick_data):,} bars)")

            # Enable auto-range
            self.chart_widget.autoRange()

        except Exception as e:
            QMessageBox.critical(self, "Chart Error", f"Failed to create candlestick chart: {str(e)}")

    def create_simple_candlesticks(self, candlestick_data):
        """Create efficient candlesticks with relative width based on positioning."""
        try:
            import numpy as np

            # Convert to numpy arrays for efficiency
            data_array = np.array(candlestick_data)
            x_vals = data_array[:, 0]
            opens = data_array[:, 1]
            highs = data_array[:, 2]
            lows = data_array[:, 3]
            closes = data_array[:, 4]

            print(f"Creating candlesticks for {len(candlestick_data)} bars")
            print(f"First 3 bars: {candlestick_data[:3]}")

            # Calculate relative width based on spacing
            if len(x_vals) > 1:
                # Find minimum spacing between candles
                x_sorted = np.sort(x_vals)
                min_spacing = np.min(np.diff(x_sorted))
                # Width should be a fraction of the minimum spacing
                relative_width = min_spacing * 0.8  # 80% of minimum spacing
                print(f"Calculated relative width: {relative_width:.4f} (min spacing: {min_spacing:.4f})")
            else:
                relative_width = 0.1  # Default for single candle

            # Create all wicks efficiently
            wick_x = np.repeat(x_vals, 3)  # x, x, nan for each wick
            wick_y = np.column_stack([lows, highs, np.full(len(x_vals), np.nan)]).flatten()
            self.chart_widget.plot(wick_x, wick_y, pen=pg.mkPen('white', width=1), connect='finite')

            # Separate bullish and bearish candles
            bullish_mask = closes >= opens
            bearish_mask = ~bullish_mask

            # Create bullish candles (green) with relative width
            if np.any(bullish_mask):
                bull_x = x_vals[bullish_mask]
                bull_opens = opens[bullish_mask]
                bull_closes = closes[bullish_mask]
                bull_heights = bull_closes - bull_opens

                # Only create bars with height > 0
                valid_mask = bull_heights > 0
                if np.any(valid_mask):
                    bull_bar = pg.BarGraphItem(
                        x=bull_x[valid_mask],
                        height=bull_heights[valid_mask],
                        width=relative_width,  # Use calculated relative width
                        y0=bull_opens[valid_mask],
                        brush='g',
                        pen='g'
                    )
                    self.chart_widget.addItem(bull_bar)

            # Create bearish candles (red) with relative width
            if np.any(bearish_mask):
                bear_x = x_vals[bearish_mask]
                bear_opens = opens[bearish_mask]
                bear_closes = closes[bearish_mask]
                bear_heights = bear_opens - bear_closes

                # Only create bars with height > 0
                valid_mask = bear_heights > 0
                if np.any(valid_mask):
                    bear_bar = pg.BarGraphItem(
                        x=bear_x[valid_mask],
                        height=bear_heights[valid_mask],
                        width=relative_width,  # Use calculated relative width
                        y0=bear_closes[valid_mask],
                        brush='r',
                        pen='r'
                    )
                    self.chart_widget.addItem(bear_bar)

            print(f"Candlesticks created successfully with width: {relative_width:.4f}")

        except Exception as e:
            print(f"Error creating simple candlesticks: {e}")
            import traceback
            traceback.print_exc()

    # ===== INDEPENDENT ZONE CALCULATION METHODS =====

    def calculate_zones_independently(self):
        """Calculate intraday zones independently using backend services directly."""
        try:
            print("BacktesterTab: Starting independent zone calculation...")

            # Check if we have universal controls data
            if not self.universal_controls_data:
                raise Exception("No universal controls data available for zone calculation")

            # Extract market data from universal controls
            market_data = self.universal_controls_data
            market_data = self._apply_historical_filter_to_market_data(market_data)

            # Get volatility data from market data
            volatility_high_data = market_data.get('volatility_high_data', [])
            volatility_low_data = market_data.get('volatility_low_data', [])

            if not volatility_high_data or not volatility_low_data:
                raise Exception("No volatility data available in universal controls data")

            print(f"Using {len(volatility_high_data)} high and {len(volatility_low_data)} low volatility entries")

            # Create zones service and calculate zones
            zones_service = VolgraphZonesService()
            zones_result = zones_service.calculate_zones(
                volatility_high_data,
                volatility_low_data,
                market_data,
                None,  # calculation_params
                0  # filter_type (H/L matching)
            )

            print(f"Independent zone calculation completed: {len(zones_result.zones)} zones calculated")
            return zones_result

        except Exception as e:
            print(f"Error in independent zone calculation: {e}")
            import traceback
            traceback.print_exc()
            return None

    def calculate_pivot_zones_independently(self):
        """Calculate pivot zones independently using backend services directly."""
        try:
            print("BacktesterTab: Starting independent pivot zone calculation...")

            # Check if we have universal controls data
            if not self.universal_controls_data:
                raise Exception("No universal controls data available for pivot zone calculation")

            # Extract market data from universal controls
            market_data = self.universal_controls_data
            market_data = self._apply_historical_filter_to_market_data(market_data)

            # Get volatility data from market data
            volatility_high_data = market_data.get('volatility_high_data', [])
            volatility_low_data = market_data.get('volatility_low_data', [])

            if not volatility_high_data or not volatility_low_data:
                raise Exception("No volatility data available in universal controls data")

            print(f"Using {len(volatility_high_data)} high and {len(volatility_low_data)} low volatility entries")

            # Create pivot zone service and calculate pivot zones
            pivot_service = PivotZoneService()
            pivot_zones_result = pivot_service.calculate_pivot_zones_from_volatility_data(
                volatility_high_data,
                volatility_low_data,
                market_data
            )

            print(f"Independent pivot zone calculation completed: {len(pivot_zones_result.zones)} pivot zones calculated")
            return pivot_zones_result

        except Exception as e:
            print(f"Error in independent pivot zone calculation: {e}")
            import traceback
            traceback.print_exc()
            return None


    def _rebase_volatility_rows_to_close(self, rows, baseline_close):
        """Recalculate dollar columns from %Change using the provided baseline_close.
        Expected row format: [Idx, Weekday, Category, $Change, %Change, $ProjChange, Projected, ...]
        Leaves non-conforming rows unchanged; returns a new list (does not mutate input).
        """
        try:
            if rows is None or baseline_close is None:
                return rows
            out = []
            for row in rows:
                try:
                    rr = list(row) if not isinstance(row, list) else row.copy()
                    if len(rr) > 6:
                        pct_val = float(rr[4])
                        proj_change = (pct_val * float(baseline_close)) / 100.0
                        rr[5] = proj_change           # $Projected Change
                        rr[6] = float(baseline_close) + proj_change  # Projected
                        rr[3] = proj_change           # $Change
                        out.append(rr)
                    else:
                        out.append(row)
                except Exception:
                    out.append(row)
            return out
        except Exception:
            return rows

    def calculate_weekly_zones_independently(self):
        """Calculate weekly zones (HL-matching vs Weekday-matching stats) using backend services.
        Returns a dict with a 'zones' list of entries: {'lower': float, 'upper': float, 'category': 'high'|'low', 'stat': str}
        The calculation respects current historical filter state.
        """
        try:
            print("BacktesterTab: Starting independent weekly zone calculation...")

            # Use universal controls data with historical filter applied (if enabled)
            if not self.universal_controls_data:
                print("No universal controls data available for weekly zones")
                return None

            md = self._apply_historical_filter_to_market_data(self.universal_controls_data)

            # Build volatility rows from the 5-FWL Projected OHLC (to match Volatility Statistics behavior exactly)
            high_data = []
            low_data = []
            try:
                # Fetch 5-FWL projected rows from universal data (not md, since md may not carry this key)
                projected_ohlc_5fwl = (self.universal_controls_data or {}).get('projected_ohlc_table_rows_5fwl', [])
                # Historical: slice to cutoff inclusive
                if getattr(self, 'viewing_historical', False):
                    cutoff_idx = getattr(self, 'historical_index', None)
                    if cutoff_idx is not None:
                        projected_ohlc_5fwl = projected_ohlc_5fwl[:int(cutoff_idx) + 1]
                        print(f"Weekly Zones (Backtester): using 5-FWL projected OHLC trimmed to {len(projected_ohlc_5fwl)} rows (<= idx {cutoff_idx})")
                # Extract volatility rows via the same backend helper
                if (not hasattr(self, '_data_service')) or self._data_service is None:
                    self._data_service = DataService()
                if projected_ohlc_5fwl:
                    vs = self._data_service._prepare_volatility_statistics_data(projected_ohlc_5fwl)
                    high_data = vs.get('volatility_high_data', [])
                    low_data = vs.get('volatility_low_data', [])
            except Exception as e:
                print(f"Weekly Zones (Backtester): extraction from 5-FWL projected OHLC failed: {e}")

            # Fallback to existing 5-FWL volatility arrays if extraction failed
            if not high_data or not low_data:
                high_data = md.get('volatility_high_data_5fwl', md.get('volatility_high_data', []))
                low_data = md.get('volatility_low_data_5fwl', md.get('volatility_low_data', []))

            if not high_data or not low_data:
                print("No volatility data available for weekly zones calculation")
                return None


            # Rebase volatility rows to the baseline close so dollar columns reflect historical/current context
            baseline_close = None
            try:
                close_arr = md.get('close')
                if close_arr is not None and hasattr(close_arr, '__len__') and len(close_arr) > 0:
                    baseline_close = float(close_arr[-1])
            except Exception:
                baseline_close = None
            if baseline_close is not None:
                high_data = self._rebase_volatility_rows_to_close(high_data, baseline_close)
                low_data = self._rebase_volatility_rows_to_close(low_data, baseline_close)
                try:
                    print(f"Weekly Zones (Backtester): rebased to baseline close ${baseline_close:.2f}")
                except Exception:
                    pass

            # Lazily create a DataService for filtering
            if not hasattr(self, '_data_service') or self._data_service is None:
                self._data_service = DataService()

            # Apply filters for H/L matching (0) and Weekday matching (1)
            hl_filtered = self._data_service.apply_volatility_statistics_filter(high_data, low_data, 0)
            wk_filtered = self._data_service.apply_volatility_statistics_filter(high_data, low_data, 1)

            hl_high = hl_filtered.get('volatility_filtered_high_data', [])
            hl_low = hl_filtered.get('volatility_filtered_low_data', [])
            wk_high = wk_filtered.get('volatility_filtered_high_data', [])
            wk_low = wk_filtered.get('volatility_filtered_low_data', [])

            vol_calc = VolatilityCalculationsService()
            hl_calc = vol_calc.calculate_volatility_data(hl_high, hl_low, md) or {}
            wk_calc = vol_calc.calculate_volatility_data(wk_high, wk_low, md) or {}

            hl_highs_stats = hl_calc.get('highs_stats', {}) or {}
            hl_lows_stats = hl_calc.get('lows_stats', {}) or {}
            wk_highs_stats = wk_calc.get('highs_stats', {}) or {}
            wk_lows_stats = wk_calc.get('lows_stats', {}) or {}

            zones = []
            for key in ('min_avg', 'median', 'average', 'max_avg'):
                # Highs pair
                v1 = hl_highs_stats.get(key)
                v2 = wk_highs_stats.get(key)
                try:
                    if v1 is not None and v2 is not None:
                        lo = float(min(v1, v2))
                        hi = float(max(v1, v2))
                        zones.append({'lower': lo, 'upper': hi, 'category': 'high', 'stat': key})
                except Exception:
                    pass
                # Lows pair
                v3 = hl_lows_stats.get(key)
                v4 = wk_lows_stats.get(key)
                try:
                    if v3 is not None and v4 is not None:
                        lo = float(min(v3, v4))
                        hi = float(max(v3, v4))
                        zones.append({'lower': lo, 'upper': hi, 'category': 'low', 'stat': key})
                except Exception:
                    pass

            if zones:
                print(f"Weekly zones computed: {len(zones)} bands")
                return {'zones': zones}
            else:
                print("No weekly zone bands computed")
                return None
        except Exception as e:
            print(f"Error calculating weekly zones: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _add_weekly_zones_to_day(self, weekly_zones, day_index):
        """Render weekly zones for a specific day index across 0.9x width as semi-transparent bands.
        weekly_zones: list of {'lower': float, 'upper': float, 'category': 'high'|'low', 'stat': str}
        """
        try:
            if not self.chart_widget or not weekly_zones:
                return

            from PyQt6.QtGui import QColor

            for z in weekly_zones:
                lo = z.get('lower'); hi = z.get('upper')
                if lo is None or hi is None:
                    continue
                if hi < lo:
                    lo, hi = hi, lo
                # Weekly zone color mapping per your spec (20% opacity)
                stat = str(z.get('stat', '') or '')
                zone_color = self._get_weekly_zone_color(stat)
                qc = QColor(zone_color)
                qc.setAlpha(51)  # ~20% opacity

                x_day = [day_index, day_index + 0.9, day_index + 0.9, day_index, day_index]
                y_zone = [lo, lo, hi, hi, lo]

                zone_fill = pg.PlotCurveItem(
                    x_day, y_zone,
                    fillLevel=lo,


                    brush=pg.mkBrush(qc),
                    pen=pg.mkPen(None)
                )
                # Set initial visibility based on checkbox state
                zone_fill.setVisible(self.show_weekly_zones_checkbox.isChecked())
                self.chart_widget.addItem(zone_fill)
                self._zone_items.append(zone_fill)
                self._weekly_zone_items.append(zone_fill)
        except Exception as e:
            print(f"Error adding weekly zones to day {day_index}: {e}")

    def _add_weekly_minavg_zones_to_day(self, weekly_zones, day_index):
        """Render MinAvg-only weekly zones for a specific day index across 0.9x width as semi-transparent bands.
        weekly_zones: list already filtered to stat == 'min_avg'
        """
        try:
            if not self.chart_widget or not weekly_zones:
                return

            from PyQt6.QtGui import QColor

            for z in weekly_zones:
                lo = z.get('lower'); hi = z.get('upper')
                if lo is None or hi is None:
                    continue
                if hi < lo:
                    lo, hi = hi, lo
                stat = str(z.get('stat', '') or '')
                zone_color = self._get_weekly_zone_color(stat)
                qc = QColor(zone_color)
                qc.setAlpha(51)  # ~20% opacity

                x_day = [day_index, day_index + 0.9, day_index + 0.9, day_index, day_index]
                y_zone = [lo, lo, hi, hi, lo]

                zone_fill = pg.PlotCurveItem(
                    x_day, y_zone,
                    fillLevel=lo,
                    brush=pg.mkBrush(qc),
                    pen=pg.mkPen(None)
                )
                # Initial visibility is controlled by the MinAvg checkbox
                zone_fill.setVisible(self.show_minavg_weekly_zones_checkbox.isChecked())
                self.chart_widget.addItem(zone_fill)
                self._zone_items.append(zone_fill)
                self._weekly_minavg_zone_items.append(zone_fill)
        except Exception as e:
            print(f"Error adding MinAvg weekly zones to day {day_index}: {e}")



    def _add_weekly_zones_to_span(self, weekly_zones, start_day_index, end_day_index):
        """Render weekly zones across a span of day indices [start, end] as semi-transparent bands."""
        try:
            if not self.chart_widget or not weekly_zones:
                return
            from PyQt6.QtGui import QColor
            x0 = start_day_index
            x1 = end_day_index + 0.9
            for z in weekly_zones:
                lo = z.get('lower'); hi = z.get('upper')
                if lo is None or hi is None:
                    continue
                if hi < lo:
                    lo, hi = hi, lo
                stat = str(z.get('stat', '') or '')
                zone_color = self._get_weekly_zone_color(stat)
                qc = QColor(zone_color); qc.setAlpha(51)
                x_span = [x0, x1, x1, x0, x0]
                y_zone = [lo, lo, hi, hi, lo]
                zone_fill = pg.PlotCurveItem(
                    x_span, y_zone,
                    fillLevel=lo,
                    brush=pg.mkBrush(qc),
                    pen=pg.mkPen(None)
                )
                zone_fill.setVisible(self.show_weekly_zones_checkbox.isChecked())
                self.chart_widget.addItem(zone_fill)
                self._zone_items.append(zone_fill)
                self._weekly_zone_items.append(zone_fill)
        except Exception as e:
            print(f"Error adding weekly zones to span {start_day_index}-{end_day_index}: {e}")

    def _add_weekly_minavg_zones_to_span(self, weekly_zones, start_day_index, end_day_index):
        """Render MinAvg-only weekly zones across a span of day indices [start, end].
        weekly_zones should already be filtered to stat == 'min_avg'.
        """
        try:
            if not self.chart_widget or not weekly_zones:
                return
            from PyQt6.QtGui import QColor
            x0 = start_day_index
            x1 = end_day_index + 0.9
            for z in weekly_zones:
                lo = z.get('lower'); hi = z.get('upper')
                if lo is None or hi is None:
                    continue
                if hi < lo:
                    lo, hi = hi, lo
                stat = str(z.get('stat', '') or '')
                zone_color = self._get_weekly_zone_color(stat)
                qc = QColor(zone_color); qc.setAlpha(51)
                x_span = [x0, x1, x1, x0, x0]
                y_zone = [lo, lo, hi, hi, lo]
                zone_fill = pg.PlotCurveItem(
                    x_span, y_zone,
                    fillLevel=lo,
                    brush=pg.mkBrush(qc),
                    pen=pg.mkPen(None)
                )
                zone_fill.setVisible(self.show_minavg_weekly_zones_checkbox.isChecked())
                self.chart_widget.addItem(zone_fill)
                self._zone_items.append(zone_fill)
                self._weekly_minavg_zone_items.append(zone_fill)
        except Exception as e:
            print(f"Error adding MinAvg weekly zones to span {start_day_index}-{end_day_index}: {e}")


    # ===== Historical Data Mode (like volatility_statistics, but runs backtester zones) =====
    def _on_historical_data_button_clicked(self):
        """Open the historical data selection dialog."""
        try:
            self._show_historical_dialog()
        except Exception as e:
            print(f"Error opening historical data dialog: {e}")

    def _show_historical_dialog(self):
        """Show a dialog to select historical date and time."""
        try:
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QDateEdit, QTimeEdit, QPushButton
            from PyQt6.QtCore import QDate, QTime

            dialog = QDialog(self)
            dialog.setWindowTitle("Select Historical Date/Time")
            dialog.setMinimumWidth(380)
            dialog.setStyleSheet("background-color: #1e1e1e; color: #ffffff;")

            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(16, 16, 16, 16)
            layout.setSpacing(10)

            # Date row
            date_row = QHBoxLayout()
            date_label = QLabel("Date:")
            date_edit = QDateEdit()
            date_edit.setCalendarPopup(True)
            date_edit.setDisplayFormat("yyyy-MM-dd")
            # Time row
            time_row = QHBoxLayout()
            time_label = QLabel("Time:")
            time_edit = QTimeEdit()
            time_edit.setDisplayFormat("HH:mm:ss")

            # Defaults: use last available 'actual_dates' if present
            try:
                default_dt = None
                if getattr(self, 'universal_controls_data', None):
                    actual_dates = self.universal_controls_data.get('actual_dates', [])
                    if actual_dates:
                        import pandas as pd
                        last_dt = pd.to_datetime(actual_dates[-1])
                        default_dt = last_dt.to_pydatetime()
                import datetime
                if default_dt is None:
                    default_dt = datetime.datetime.now()
                date_edit.setDate(QDate(default_dt.year, default_dt.month, default_dt.day))
                time_edit.setTime(QTime(default_dt.hour, default_dt.minute, default_dt.second))
            except Exception:
                pass

            date_row.addWidget(date_label)
            date_row.addWidget(date_edit)
            time_row.addWidget(time_label)
            time_row.addWidget(time_edit)
            layout.addLayout(date_row)
            layout.addLayout(time_row)

            # Buttons
            btn_row = QHBoxLayout()
            load_btn = QPushButton("Load Historical Data")
            cancel_btn = QPushButton("Cancel")
            load_btn.clicked.connect(lambda: self._load_historical_from_dialog(dialog, date_edit, time_edit))
            cancel_btn.clicked.connect(dialog.reject)
            btn_row.addWidget(load_btn)
            btn_row.addWidget(cancel_btn)
            layout.addLayout(btn_row)

            dialog.exec()
        except Exception as e:
            print(f"Error showing historical dialog: {e}")

    def _load_historical_from_dialog(self, dialog, date_edit, time_edit):
        """Combine date/time from dialog and trigger historical mode."""
        try:
            import datetime
            d = date_edit.date()
            t = time_edit.time()
            ts = datetime.datetime(d.year(), d.month(), d.day(), t.hour(), t.minute(), t.second())
            self._load_historical_data(ts)
            dialog.accept()
        except Exception as e:
            print(f"Error loading historical data: {e}")

    def _load_historical_data(self, timestamp):
        """Enable historical mode and recompute zones using data up to timestamp."""
        try:
            if not getattr(self, 'universal_controls_data', None):
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "Error", "No centralized market data available yet.")
                return

            cutoff_idx = self._find_cutoff_index_for_timestamp(timestamp)
            if cutoff_idx is None:
                print(f"Historical mode: no matching timestamps found, leaving current data unchanged")
                return

            # Store historical context
            self.viewing_historical = True
            self.historical_timestamp = timestamp
            self.historical_index = cutoff_idx
            print(f"Historical mode enabled at index {cutoff_idx} for {timestamp}")

            # Recreate zones (both intraday and pivot) using historical filter
            self.create_zones_for_all_days()
        except Exception as e:
            print(f"Error in _load_historical_data: {e}")

    def _session_from_timestamp(self, ts):
        """Return '1'..'5' session code using 18:00 boundary (Sun 18:00 .. Fri 16:59).
        Mirrors logic used elsewhere so day-index classification is consistent.
        """
        try:
            import pandas as pd
            t = pd.Timestamp(ts)
            time_part = t.time(); dow = t.weekday()  # Mon=0..Sun=6
            session_start = pd.Timestamp('18:00:00').time()
            if time_part >= session_start:
                # After 18:00 -> next day's session mapping
                if dow == 6: return '1'   # Sunday after 18:00 -> Monday session
                if dow == 0: return '2'
                if dow == 1: return '3'
                if dow == 2: return '4'
                if dow == 3: return '5'
                return 'Unknown'
            else:
                # Before 18:00 -> current day's session mapping
                if dow == 0: return '1'   # Monday before 18:00 -> Monday session
                if dow == 1: return '2'
                if dow == 2: return '3'
                if dow == 3: return '4'
                if dow == 4: return '5'
                return 'Unknown'
        except Exception:
            return 'Unknown'

    def _closest_previous_friday_timestamp(self, ts):
        """Return a timestamp for the closest previous Friday at 16:59 local time relative to ts."""
        try:
            import pandas as pd
            t = pd.Timestamp(ts)
            weekday = t.weekday()  # Mon=0..Sun=6
            days_since_friday = (weekday - 4) % 7  # 0 if Friday, 1 if Saturday, ...
            prev_fri_date = (t.normalize() - pd.Timedelta(days=int(days_since_friday)))
            prev_fri_ts = prev_fri_date + pd.Timedelta(hours=16, minutes=59)
            return prev_fri_ts.to_pydatetime()
        except Exception:
            return ts


    def _find_cutoff_index_for_timestamp(self, timestamp):
        """Find last index with datetime <= timestamp using best-aligned time source.
        Preference order: first column of 'projected_ohlc_table_rows' (aligned with volatility data),
        then 'timestamp', then 'actual_dates'.
        """
        try:
            if not getattr(self, 'universal_controls_data', None):
                return None
            md = self.universal_controls_data
            import pandas as pd

            ts = pd.to_datetime(timestamp)

            # 1) Prefer projected OHLC dates (same indexing as volatility arrays)
            proj_rows = md.get('projected_ohlc_table_rows') or []
            if proj_rows:
                try:
                    first_col = [row[0] for row in proj_rows if len(row) > 0]
                    if first_col:
                        dts = pd.DatetimeIndex(pd.to_datetime(first_col))
                        pos = dts.searchsorted(ts, side='right') - 1
                        if pos >= 0:
                            print(f"Historical cutoff via projected_ohlc: idx={pos}, date={dts[pos]}")
                            return int(pos)
                except Exception as e:
                    print(f"Projected OHLC cutoff detection failed: {e}")

            # 2) Fallback to 'timestamp' array if available
            ts_arr = md.get('timestamp')
            if ts_arr is not None and hasattr(ts_arr, '__len__') and len(ts_arr) > 0:
                dti = pd.DatetimeIndex(pd.to_datetime(ts_arr))
                pos = dti.searchsorted(ts, side='right') - 1
                if pos >= 0:
                    print(f"Historical cutoff via timestamp: idx={pos}, date={dti[pos]}")
                    return int(pos)

            # 3) Fallback to 'actual_dates'
            actual_dates = md.get('actual_dates', [])
            if actual_dates:
                adi = pd.DatetimeIndex(pd.to_datetime(actual_dates))
                pos = adi.searchsorted(ts, side='right') - 1
                if pos >= 0:
                    print(f"Historical cutoff via actual_dates: idx={pos}, date={adi[pos]}")
                    return int(pos)

            return None
        except Exception as e:
            print(f"Error finding cutoff index: {e}")
            return None

    def _apply_historical_filter_to_market_data(self, market_data):
        """Create a copy of market_data limited to the historical cutoff and adjust current close/projected values.
        Mirrors volatility_statistics.py so zones match in historical mode.
        """
        try:
            if not getattr(self, 'viewing_historical', False):
                return market_data
            if getattr(self, 'historical_index', None) is None:
                return market_data

            cutoff_inclusive = int(self.historical_index)
            md = dict(market_data)
            print(f"Historical filter: requested cutoff idx = {cutoff_inclusive}")

            # Slice volatility arrays to cutoff (inclusive), clamping cutoff per-array
            for key in (
                'volatility_high_data', 'volatility_low_data',
                'volatility_filtered_high_data', 'volatility_filtered_low_data',
                'volatility_high_data_5fwl', 'volatility_low_data_5fwl',
                'volatility_filtered_high_data_5fwl', 'volatility_filtered_low_data_5fwl'
            ):
                arr = market_data.get(key)
                if isinstance(arr, (list, tuple)) and len(arr) > 0:
                    used_cutoff = min(cutoff_inclusive, len(arr) - 1)
                    md[key] = [list(row) if isinstance(row, (list, tuple)) else row for row in arr[:used_cutoff + 1]]
                    print(f"Historical filter: {key} -> using {len(md[key])}/{len(arr)} entries (cutoff={used_cutoff})")

            # Adjust the "current" close in the copied market data to the historical close at cutoff
            close_arr = market_data.get('close')
            if close_arr is not None and hasattr(close_arr, '__len__') and len(close_arr) > 0:
                try:
                    original_current_close = float(close_arr[-1])
                    used_close_idx = min(cutoff_inclusive, len(close_arr) - 1)
                    historical_close = float(close_arr[used_close_idx])

                    # Copy and update last element
                    updated_close = close_arr.copy() if hasattr(close_arr, 'copy') else list(close_arr)
                    updated_close[-1] = historical_close
                    md['close'] = updated_close
                    print(f"Historical filter: close[-1] set to historical close at idx {used_close_idx}: {historical_close}")

                    # Recalculate projected values (column 6) in volatility arrays to be relative to historical close
                    def adjust_projected(arr_key: str):
                        rows = md.get(arr_key) or []
                        new_rows = []
                        for row in rows:
                            try:
                                if isinstance(row, (list, tuple)) and len(row) > 6:
                                    r = list(row)
                                    val = float(r[6])
                                    r[6] = historical_close + (val - original_current_close)
                                    new_rows.append(r)
                                else:
                                    new_rows.append(row)
                            except Exception:
                                new_rows.append(row)
                        md[arr_key] = new_rows

                    adjust_projected('volatility_high_data')
                    adjust_projected('volatility_low_data')
                    adjust_projected('volatility_high_data_5fwl')
                    adjust_projected('volatility_low_data_5fwl')
                except Exception as e:
                    print(f"Warning: could not adjust historical close/projected values: {e}")

            return md
        except Exception as e:
            print(f"Error applying historical filter: {e}")
            return market_data

    def get_projected_ohlc_data(self):
        """
        Get projected OHLC data from universal controls data.
        Returns the pre-computed projected OHLC table rows from the backend.
        """
        try:
            print("BacktesterTab: Accessing projected OHLC data...")

            # Check if we have universal controls data
            if not self.universal_controls_data:
                raise Exception("No universal controls data available for projected OHLC access")

            # Extract projected OHLC data from universal controls
            market_data = self.universal_controls_data
            projected_ohlc_rows = market_data.get('projected_ohlc_table_rows', [])

            if not projected_ohlc_rows:
                raise Exception("No projected OHLC data available in universal controls data")

            print(f"BacktesterTab: Found {len(projected_ohlc_rows)} projected OHLC rows")
            return projected_ohlc_rows

        except Exception as e:
            print(f"BacktesterTab: Error accessing projected OHLC data: {e}")
            raise

    def get_projected_ohlc_summary(self):
        """
        Get a summary of projected OHLC data including key statistics.
        Returns a dictionary with summary information about the projected OHLC data.
        """
        try:
            projected_ohlc_rows = self.get_projected_ohlc_data()

            if not projected_ohlc_rows:
                return {"error": "No projected OHLC data available"}

            # Extract projected highs and lows from the data
            # Projected OHLC structure: [Date, Weekday, Category, Updated, $Change High, $Change Low,
            #                           %Change High, %Change Low, $Projected High Change, $Projected Low Change,
            #                           Projected High, Projected Low, Open ratio, Close ratio, bg_color, fg_color]
            projected_highs = []
            projected_lows = []

            for row in projected_ohlc_rows:
                if len(row) >= 12:  # Ensure we have enough columns
                    try:
                        # Projected High is at index 10, Projected Low is at index 11
                        projected_high = float(row[10]) if row[10] and str(row[10]).strip() != '' else None
                        projected_low = float(row[11]) if row[11] and str(row[11]).strip() != '' else None

                        if projected_high is not None:
                            projected_highs.append(projected_high)
                        if projected_low is not None:
                            projected_lows.append(projected_low)
                    except (ValueError, TypeError):
                        continue

            # Calculate summary statistics
            summary = {
                "total_rows": len(projected_ohlc_rows),
                "projected_highs_count": len(projected_highs),
                "projected_lows_count": len(projected_lows),
                "max_projected_high": max(projected_highs) if projected_highs else None,
                "min_projected_high": min(projected_highs) if projected_highs else None,
                "max_projected_low": max(projected_lows) if projected_lows else None,
                "min_projected_low": min(projected_lows) if projected_lows else None,
                "avg_projected_high": sum(projected_highs) / len(projected_highs) if projected_highs else None,
                "avg_projected_low": sum(projected_lows) / len(projected_lows) if projected_lows else None,
            }

            print(f"BacktesterTab: Projected OHLC summary: {summary}")
            return summary

        except Exception as e:
            print(f"BacktesterTab: Error generating projected OHLC summary: {e}")
            return {"error": str(e)}

    def get_projected_ohlc_for_backtesting(self):
        """
        Get projected OHLC data formatted for backtesting purposes.
        Returns a list of dictionaries with structured projected OHLC data.
        """
        try:
            projected_ohlc_rows = self.get_projected_ohlc_data()

            if not projected_ohlc_rows:
                return []

            # Convert rows to structured format for backtesting
            structured_data = []

            for i, row in enumerate(projected_ohlc_rows):
                if len(row) >= 12:  # Ensure we have enough columns
                    try:
                        # Extract data from projected OHLC row
                        # Structure: [Date, Weekday, Category, Updated, $Change High, $Change Low,
                        #            %Change High, %Change Low, $Projected High Change, $Projected Low Change,
                        #            Projected High, Projected Low, Open ratio, Close ratio, ...]

                        structured_row = {
                            "index": i,
                            "date": str(row[0]) if row[0] else "",
                            "weekday": str(row[1]) if row[1] else "",
                            "category": str(row[2]) if row[2] else "",
                            "dollar_change_high": float(row[4]) if row[4] and str(row[4]).strip() != '' else 0.0,
                            "dollar_change_low": float(row[5]) if row[5] and str(row[5]).strip() != '' else 0.0,
                            "percent_change_high": float(row[6]) if row[6] and str(row[6]).strip() != '' else 0.0,
                            "percent_change_low": float(row[7]) if row[7] and str(row[7]).strip() != '' else 0.0,
                            "projected_high_change": float(row[8]) if row[8] and str(row[8]).strip() != '' else 0.0,
                            "projected_low_change": float(row[9]) if row[9] and str(row[9]).strip() != '' else 0.0,
                            "projected_high": float(row[10]) if row[10] and str(row[10]).strip() != '' else 0.0,
                            "projected_low": float(row[11]) if row[11] and str(row[11]).strip() != '' else 0.0,
                        }

                        # Add open and close ratios if available
                        if len(row) >= 14:
                            structured_row["open_ratio"] = float(row[12]) if row[12] and str(row[12]).strip() != '' else 0.0
                            structured_row["close_ratio"] = float(row[13]) if row[13] and str(row[13]).strip() != '' else 0.0

                        structured_data.append(structured_row)

                    except (ValueError, TypeError) as e:
                        print(f"BacktesterTab: Error processing row {i}: {e}")
                        continue

            print(f"BacktesterTab: Converted {len(structured_data)} projected OHLC rows for backtesting")
            return structured_data

        except Exception as e:
            print(f"BacktesterTab: Error formatting projected OHLC data for backtesting: {e}")
            return []

        except Exception as e:
            print(f"Error creating candlesticks: {e}")
            QMessageBox.critical(self, "Chart Error", f"Failed to draw candlesticks: {str(e)}")

