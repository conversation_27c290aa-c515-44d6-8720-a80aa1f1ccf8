"""
Data Service Backend
Async service for fetching market data and computing technical indicators.
"""

import asyncio
import logging
import multiprocessing as mp
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import pandas as pd
import numpy as np
import yfinance as yf
from concurrent.futures import <PERSON>PoolExecutor, ThreadPoolExecutor
import json
import sys
from pathlib import Path

# Import enterprise-grade utilities
from .buffer_utils import buffer_manager, CPP_KERNELS_AVAILABLE
from .shared_memory_manager import shared_memory_manager, zero_copy_kernel_interface
from .observability import (
    get_structured_logger,
    trace_operation,
    correlation_manager,
    metrics_collector,
)
from .options_service import OptionsService
from .volatility_calculations_service import VolatilityCalculationsService
from .density_calculations_service import DensityCalculationsService

# Try to import C++ kernels if available
try:
    # Add build directory to path first (for updated module)
    build_dir = Path(__file__).parent.parent.parent / "build" / "Release"
    if build_dir.exists() and str(build_dir) not in sys.path:
        sys.path.insert(0, str(build_dir))

    # Add compute directory to path as fallback
    compute_dir = Path(__file__).parent.parent / "compute"
    if compute_dir.exists() and str(compute_dir) not in sys.path:
        sys.path.insert(0, str(compute_dir))

    import indicators
except ImportError:
    pass


class DataService:
    """Async data service for market data fetching and processing."""

    def __init__(self):
        self.logger = get_structured_logger("DataService")

        # Executor configuration aligned with CPU cores (restored)
        cpu_count = mp.cpu_count()
        self.process_executor = ProcessPoolExecutor(
            max_workers=min(cpu_count, 4),  # Balanced parallelism without starving memory
            mp_context=mp.get_context("spawn"),  # Use spawn for better isolation
        )
        self.thread_executor = ThreadPoolExecutor(
            max_workers=min(cpu_count * 2, 8), thread_name_prefix="DataService-IO"
        )

        self.active_tasks = {}
        self.cache = {}
        self._shutdown_event = asyncio.Event()

        # Performance monitoring
        self._request_count = 0
        self._error_count = 0

        # Initialize options service
        self.options_service = OptionsService()

        # Initialize volatility calculations service
        self.volatility_calculations_service = VolatilityCalculationsService()

        # Initialize density calculations service
        self.density_calculations_service = DensityCalculationsService()

    def get_options_data(self, ticker: str, selected_expiry: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get options data for a ticker using the backend options service."""
        try:
            return self.options_service.fetch_options_data(ticker, selected_expiry)
        except Exception as e:
            self.logger.error(f"Error fetching options data for {ticker}: {e}")
            return None

    def clear_options_cache(self):
        """Clear the options data cache."""
        try:
            self.options_service.clear_cache()
            self.logger.info("Options cache cleared")
        except Exception as e:
            self.logger.error(f"Error clearing options cache: {e}")

    def get_options_cache_info(self) -> Dict[str, Any]:
        """Get information about the options cache."""
        try:
            return self.options_service.get_cache_info()
        except Exception as e:
            self.logger.error(f"Error getting options cache info: {e}")
            return {"total_entries": 0, "entries": []}

    def calculate_volatility_data(self, filtered_high_data, filtered_low_data,
                                market_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Calculate volatility chart data using the backend service."""
        try:
            return self.volatility_calculations_service.calculate_volatility_data(
                filtered_high_data, filtered_low_data, market_data
            )
        except Exception as e:
            self.logger.error(f"Error calculating volatility data: {e}")
            return {}

    def calculate_density_data(self, filtered_high_data, filtered_low_data,
                             market_data: Optional[Dict[str, Any]] = None,
                             price_type: str = 'ask', selected_expiry: Optional[str] = None) -> Dict[str, Any]:
        """Calculate density chart data using the backend service."""
        try:
            return self.density_calculations_service.calculate_density_data(
                filtered_high_data, filtered_low_data, market_data, price_type, selected_expiry
            )
        except Exception as e:
            self.logger.error(f"Error calculating density data: {e}")
            return {}

    def __post_init__(self):
        """Post-initialization setup."""
        # Store current FWL Aggr value for UI access
        self.current_fwl_aggr = 1

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging."""
        logger = logging.getLogger("DataService")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    async def initialize(self):
        """Initialize the data service."""
        self.logger.info("DataService initialized")

    async def shutdown(self):
        """Graceful shutdown of the data service."""
        self.logger.info("Shutting down DataService...")
        self._shutdown_event.set()

        # Wait for active tasks to complete
        if self.active_tasks:
            self.logger.info(
                f"Waiting for {len(self.active_tasks)} active tasks to complete"
            )
            await asyncio.gather(*self.active_tasks.values(), return_exceptions=True)

        # Shutdown both executors
        self.process_executor.shutdown(wait=True)
        self.thread_executor.shutdown(wait=True)
        self.logger.info("DataService shutdown complete")

    def _get_yfinance_interval(self, timeframe: str) -> str:
        """Convert timeframe to yfinance interval."""
        mapping = {
            "1m": "1m",
            "5m": "5m",
            "15m": "15m",
            "30m": "30m",
            "60m": "1h",
            "Daily": "1d",
        }
        return mapping.get(timeframe, "1d")

    def _get_candles_per_day(self, timeframe: str) -> int:
        """Get approximate number of candles per trading day - Backend Logic Layer."""
        # Business logic mapping - no mathematical calculations
        candles_mapping = {
            "1m": 390,  # 6.5 hours * 60 minutes
            "5m": 78,  # 390 / 5
            "15m": 26,  # 390 / 15
            "30m": 13,  # 390 / 30
            "60m": 7,  # 390 / 60 (rounded)
            "Daily": 1,  # 1 candle per day
        }
        return candles_mapping.get(timeframe, 1)

    def _get_period_from_dtl(self, dtl: int, timeframe: str) -> str:
        """Calculate period string from days to load and timeframe."""
        # Always use max period to ensure we get enough data
        if timeframe == "Daily":
            if dtl <= 5:
                return "5d"
            elif dtl <= 30:
                return "1mo"
            elif dtl <= 90:
                return "3mo"
            elif dtl <= 180:
                return "6mo"
            elif dtl <= 365:
                return "1y"
            elif dtl <= 730:
                return "2y"
            elif dtl <= 1825:
                return "5y"
            else:
                return "max"  # Use max available data
        else:
            # For intraday data, use max period to get all available data
            return "max"

    def _fetch_data_sync(self, ticker: str, timeframe: str, dtl: int) -> pd.DataFrame:
        """Synchronous data fetching (runs in executor)."""
        try:
            interval = self._get_yfinance_interval(timeframe)
            period = self._get_period_from_dtl(dtl, timeframe)

            # Create ticker object
            ticker_obj = yf.Ticker(ticker)

            # Fetch data
            data = ticker_obj.history(period=period, interval=interval)

            if data.empty:
                raise ValueError(f"No data found for {ticker}")

            # Limit to requested number of DAYS (not candles)
            if not data.empty:
                # Calculate how many candles represent the requested days
                if timeframe == "Daily":
                    # For daily data, DTL is straightforward
                    if len(data) > dtl:
                        data = data.tail(dtl)
                else:
                    # For intraday data, delegate all calculations to C++
                    candles_per_day = self._get_candles_per_day(timeframe)
                    if not CPP_KERNELS_AVAILABLE:
                        raise RuntimeError(
                            "C++ kernels are required for data calculations"
                        )

                    # Use C++ for mathematical calculation - no computing in backend
                    try:
                        result = indicators.calculate_total_candles_needed(
                            dtl, candles_per_day
                        )
                        if result.success:
                            total_candles_needed = result.data
                        else:
                            raise RuntimeError(
                                f"C++ calculation failed: {result.error_message}"
                            )
                    except Exception as e:
                        raise RuntimeError(f"C++ kernel error: {e}") from e

                    if len(data) > total_candles_needed:
                        data = data.tail(total_candles_needed)

            return data

        except Exception as e:
            self.logger.error(f"Error fetching data for {ticker}: {str(e)}")
            raise

    def _calculate_donchian_midpoint(self, closes, length: int):
        """Calculate Donchian midpoint using C++ kernels with enterprise-grade zero-copy buffer passing."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use enterprise-grade buffer manager for optimal performance
            result = buffer_manager.call_cpp_kernel_with_optimal_buffers(
                "calculate_donchian_midpoint", closes, length
            )

            if result.success:
                return result.data  # Return data without unnecessary conversions
            else:
                raise RuntimeError(
                    f"C++ Donchian midpoint calculation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(f"C++ kernel error in Donchian midpoint calculation: {e}")
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _calculate_donchian_midpoint_with_skip(self, closes, length: int):
        """Apply skip logic to Donchian midpoint using enterprise-grade zero-copy buffer management."""
        # Get full calculation from C++ computing layer
        donchian_values = self._calculate_donchian_midpoint(closes, length)

        # Apply skip logic using C++ computing with optimal buffer passing
        skip_count = length + 1

        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use enterprise-grade buffer manager for optimal performance
            result = buffer_manager.call_cpp_kernel_with_optimal_buffers(
                "apply_skip_logic_to_array", donchian_values, skip_count
            )

            if result.success:
                return result.data  # Return data without unnecessary conversions
            else:
                raise RuntimeError(f"C++ skip logic failed: {result.error_message}")
        except Exception as e:
            self.logger.error(f"C++ kernel error in skip logic: {e}")
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _calculate_bollinger_bandwidth(self, closes, length: int):
        """Calculate Bollinger Bandwidth using enterprise-grade zero-copy buffer management."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use enterprise-grade buffer manager for optimal performance
            result = buffer_manager.call_cpp_kernel_with_optimal_buffers(
                "calculate_bollinger_bandwidth", closes, length, 2.0
            )

            if result.success:
                return result.data  # Return data without unnecessary conversions
            else:
                raise RuntimeError(
                    f"C++ Bollinger Bandwidth calculation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(
                f"C++ kernel error in Bollinger Bandwidth calculation: {e}"
            )
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _calculate_bollinger_bandwidth_with_skip(self, closes, length: int):
        """Apply skip logic to Bollinger Bandwidth using enterprise-grade zero-copy buffer management."""
        # Get full calculation from C++ computing layer
        bandwidth_values = self._calculate_bollinger_bandwidth(closes, length)

        # Apply skip logic using C++ computing with optimal buffer passing
        skip_count = length + 1

        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use enterprise-grade buffer manager for optimal performance
            result = buffer_manager.call_cpp_kernel_with_optimal_buffers(
                "apply_skip_logic_to_array", bandwidth_values, skip_count
            )

            if result.success:
                return result.data  # Return data without unnecessary conversions
            else:
                raise RuntimeError(f"C++ skip logic failed: {result.error_message}")
        except Exception as e:
            self.logger.error(f"C++ kernel error in bandwidth skip logic: {e}")
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _calculate_donchian_strip_segments(self, closes, donchian_midpoint_50) -> list:
        """Calculate Donchian indicator strip segments using enterprise-grade zero-copy buffer management."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use enterprise-grade buffer manager for optimal performance
            result = buffer_manager.call_cpp_kernel_with_optimal_buffers(
                "calculate_donchian_strip_segments", closes, donchian_midpoint_50
            )

            if result.success:
                # Convert C++ segments to Python dictionaries for backend logic processing
                segments = []
                for segment in result.data:
                    segments.append(
                        {
                            "start_idx": segment.start_idx,
                            "end_idx": segment.end_idx,
                            "color": segment.color,
                        }
                    )
                return segments
            else:
                raise RuntimeError(
                    f"C++ Donchian strip calculation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(f"C++ kernel error in Donchian strip calculation: {e}")
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _calculate_donchian_strip_segments_with_skip(
        self, closes, donchian_midpoint_50, skip_length: int
    ) -> list:
        """Apply skip logic to Donchian strip segments - Backend Logic Layer."""
        # Get full segments from C++ computing layer
        full_segments = self._calculate_donchian_strip_segments(
            closes, donchian_midpoint_50
        )

        # Apply skip logic - only keep segments that start from skip_count onwards
        skip_count = skip_length + 1
        filtered_segments = []

        for segment in full_segments:
            if segment["start_idx"] >= skip_count:
                filtered_segments.append(segment)
            elif segment["end_idx"] >= skip_count:
                # Segment spans across skip boundary, adjust start index
                adjusted_segment = segment.copy()
                adjusted_segment["start_idx"] = skip_count
                filtered_segments.append(adjusted_segment)

        return filtered_segments

    def _track_vector_direction_changes(self, vector) -> list:
        """Track vector direction changes using enterprise-grade zero-copy buffer management."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use enterprise-grade buffer manager for optimal performance
            result = buffer_manager.call_cpp_kernel_with_optimal_buffers(
                "track_vector_direction_changes", vector
            )

            if result.success:
                return result.data
            else:
                raise RuntimeError(
                    f"C++ vector direction tracking failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(f"C++ kernel error in vector direction tracking: {e}")
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _calculate_rebased_prices(
        self, opens, highs, lows, closes, vector, cycles: list
    ) -> tuple:
        """Calculate rebased prices using enterprise-grade zero-copy buffer management."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use enterprise-grade buffer manager for optimal performance
            result = buffer_manager.call_cpp_kernel_with_optimal_buffers(
                "calculate_rebased_prices", opens, highs, lows, closes, vector, cycles
            )

            if result.success:
                rebased_data, rebased_vector = result.data
                # Convert RebasedOHLC objects to tuples for backend logic processing
                rebased_tuples = [
                    (r.index, r.open_pct, r.high_pct, r.low_pct, r.close_pct)
                    for r in rebased_data
                ]
                return rebased_tuples, rebased_vector
            else:
                raise RuntimeError(
                    f"C++ rebased price calculation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(f"C++ kernel error in rebased price calculation: {e}")
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _apply_skip_logic_to_rebased_prices(
        self, rebased_data: list, rebased_vector: list, length: int
    ) -> tuple:
        """Apply skip logic to rebased prices - Backend Logic Layer."""
        # Business rule: skip first (length + 1) candles
        skip_count = length + 1

        # Apply skip logic using pure business logic - no mathematical operations
        filtered_rebased_data = []
        filtered_rebased_vector = []

        # Define NaN placeholder for invalid data (business logic constant)
        nan_placeholder = float("nan")

        for i, (idx, open_pct, high_pct, low_pct, close_pct) in enumerate(rebased_data):
            if i < skip_count:
                # Business logic: mark skipped candles as invalid
                filtered_rebased_data.append(
                    (
                        idx,
                        nan_placeholder,
                        nan_placeholder,
                        nan_placeholder,
                        nan_placeholder,
                    )
                )
                filtered_rebased_vector.append(nan_placeholder)
            else:
                # Business logic: keep valid candles
                filtered_rebased_data.append(
                    (idx, open_pct, high_pct, low_pct, close_pct)
                )
                # Business logic: safely get vector value or use placeholder
                vector_value = (
                    rebased_vector[i] if i < len(rebased_vector) else nan_placeholder
                )
                filtered_rebased_vector.append(vector_value)

        return filtered_rebased_data, filtered_rebased_vector

    def _calculate_peaks_and_troughs(self, rebased_data: list) -> tuple:
        """Calculate peaks and troughs using C++ kernels only."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        if not rebased_data:
            return [], []

        try:
            # Extract data from rebased_data tuples: (idx, open_pct, high_pct, low_pct, close_pct)
            indices = [item[0] for item in rebased_data]
            opens = [item[1] for item in rebased_data]
            highs = [item[2] for item in rebased_data]
            lows = [item[3] for item in rebased_data]
            closes = [item[4] for item in rebased_data]

            result = indicators.calculate_peaks_and_troughs(
                indices, opens, highs, lows, closes
            )
            if result.success:
                peaks, troughs = result.data
                return peaks, troughs
            else:
                raise RuntimeError(
                    f"C++ peak/trough calculation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(f"C++ kernel error in peak/trough calculation: {e}")
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _calculate_standard_deviations(self, peaks, troughs) -> dict:
        """Calculate standard deviations based on peaks and troughs levels using C++ kernels."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use C++ to calculate separate standard deviations from peaks and troughs
            result = indicators.calculate_separated_standard_deviations_from_peaks_troughs(
                peaks, troughs
            )

            if result.success:
                return {
                    "positive_std": result.data.positive_std,
                    "negative_std": result.data.negative_std,
                    "positive_count": result.data.positive_count,
                    "negative_count": result.data.negative_count,
                }
            else:
                raise RuntimeError(
                    f"C++ standard deviation calculation failed: {result.error_message}"
                )

        except Exception as e:
            self.logger.error(
                f"C++ kernel error in standard deviation calculation: {e}"
            )
            # Return default values on error
            return {
                "positive_std": 0.0,
                "negative_std": 0.0,
                "positive_count": 0,
                "negative_count": 0,
            }

    def _calculate_mean_peak_trough_strengths(self, peaks, troughs) -> dict:
        """Calculate mean peak and trough strengths using C++ kernels - eliminates regex parsing from UI."""
        if not CPP_KERNELS_AVAILABLE:
            # Fallback: extract strengths manually (backend logic only)
            peak_strengths = []
            trough_strengths = []

            import re

            for peak in peaks:
                if hasattr(peak, "label"):
                    match = re.search(r"H \((\d+)\)", peak.label)
                    if match:
                        strength = int(match.group(1))
                        if strength > 0:
                            peak_strengths.append(float(strength))

            for trough in troughs:
                if hasattr(trough, "label"):
                    match = re.search(r"L \((\d+)\)", trough.label)
                    if match:
                        strength = int(match.group(1))
                        if strength > 0:
                            trough_strengths.append(float(strength))

            return {
                "peak_strengths": peak_strengths,
                "trough_strengths": trough_strengths,
            }

        try:
            # Use C++ to extract strengths from labels
            result = indicators.extract_peak_trough_strengths(peaks, troughs)

            if result.success:
                return {
                    "peak_strengths": result.data["peak_strengths"],
                    "trough_strengths": result.data["trough_strengths"],
                }
            else:
                raise RuntimeError(
                    f"C++ strength extraction failed: {result.error_message}"
                )

        except Exception as e:
            self.logger.error(f"C++ kernel error in strength extraction: {e}")
            # Return empty lists on error
            return {"peak_strengths": [], "trough_strengths": []}

    def _calculate_mean_peak_trough_levels(self, peaks, troughs) -> tuple:
        """Calculate mean peak and trough levels using C++ kernels only."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use C++ kernel for mean peak/trough levels calculation
            result = indicators.calculate_mean_peak_trough_levels(peaks, troughs)
            if result.success:
                # Return both the C++ object and Python dict
                cpp_object = result.data
                python_dict = {
                    "mean_peak_level": result.data.mean_peak_level,
                    "mean_trough_level": result.data.mean_trough_level,
                }
                return cpp_object, python_dict
            else:
                raise RuntimeError(
                    f"C++ mean peak/trough levels calculation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(
                f"C++ kernel error in mean peak/trough levels calculation: {e}"
            )
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _calculate_predictive_cycle_lines(
        self, peaks, troughs, strengths_data, levels_data
    ) -> dict:
        """Calculate predictive cycle line segments using C++ kernels only."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use C++ kernel for predictive cycle lines calculation
            result = indicators.calculate_predictive_cycle_lines(
                peaks, troughs, strengths_data, levels_data
            )
            if result.success:
                return result.data
            else:
                raise RuntimeError(
                    f"C++ predictive cycle lines calculation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(
                f"C++ kernel error in predictive cycle lines calculation: {e}"
            )
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _calculate_stdev_infinity_lines(
        self, peaks, troughs, zero_percent_price: float
    ) -> dict:
        """Calculate percentile levels from peaks and troughs using C++ kernels only."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use C++ kernel for percentile levels calculation from peaks and troughs
            result = indicators.calculate_percentile_levels_from_peaks_troughs(
                peaks, troughs, zero_percent_price
            )
            if result.success:
                return result.data
            else:
                raise RuntimeError(
                    f"C++ percentile levels calculation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(
                f"C++ kernel error in percentile levels calculation: {e}"
            )
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _calculate_crosshair_info_lookup(
        self, rebased_data: list, cycles: list
    ) -> dict:
        """Calculate crosshair info lookup table with corrected cycle position calculation."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use C++ kernel for crosshair info lookup calculation
            result = indicators.calculate_crosshair_info_lookup(rebased_data, cycles)
            if result.success:
                crosshair_lookup = result.data

                # Override the incorrect C++ cycle position calculation with correct Python implementation
                self.logger.info("Overriding C++ cycle position calculation with corrected Python implementation")
                crosshair_lookup = self._fix_cycle_position_calculation(crosshair_lookup, rebased_data)

                return crosshair_lookup
            else:
                raise RuntimeError(
                    f"C++ crosshair info lookup calculation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(
                f"C++ kernel error in crosshair info lookup calculation: {e}"
            )
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _fix_cycle_position_calculation(self, crosshair_lookup: dict, rebased_data: list) -> dict:
        """
        Fix the incorrect C++ cycle position calculation with correct Python implementation.
        Uses the EXACT same algorithm as peak/trough detection but applied to every candle.
        """
        try:
            if not rebased_data:
                return crosshair_lookup

            # Extract close prices from rebased data (same as peak/trough algorithm)
            closes = [item[4] for item in rebased_data]  # close_pct is at index 4
            indices = [item[0] for item in rebased_data]  # index is at index 0

            # Apply the EXACT same algorithm as calculate_peaks_and_troughs
            current_cycle = ""
            cycle_start_index = -1

            for i, (current_index, close_price) in enumerate(zip(indices, closes)):
                # Determine cycle based on close price (EXACT same logic as peak/trough detection)
                cycle = "bullish" if close_price >= 0.0 else "bearish"

                if not current_cycle:
                    # First candle
                    current_cycle = cycle
                    cycle_start_index = current_index
                else:
                    if cycle != current_cycle:
                        # Cycle changed, update cycle start
                        current_cycle = cycle
                        cycle_start_index = current_index

                # Calculate strength using EXACT same formula as peak/trough detection
                candle_count = current_index - cycle_start_index + 1

                # Generate label using EXACT same format as peak/trough detection
                if current_cycle == "bullish":
                    category = f"H{candle_count}"
                else:
                    category = f"L{candle_count}"

                # Update the crosshair lookup with correct category
                candle_index_str = str(i)  # Use rebased index
                if candle_index_str in crosshair_lookup:
                    crosshair_lookup[candle_index_str]["category"] = category

            self.logger.info(f"Fixed cycle position calculation for {len(crosshair_lookup)} candles using exact peak/trough algorithm")
            return crosshair_lookup

        except Exception as e:
            self.logger.error(f"Error fixing cycle position calculation: {e}")
            return crosshair_lookup  # Return original if fixing fails

    def _calculate_correct_cycle_position(self, candle_index: int, cycles: list) -> str:
        """
        Calculate correct cycle position using the same strength calculation logic as peak/trough detection.
        This is the Python implementation of the corrected C++ calculate_cycle_position function.
        """
        try:
            if not cycles or candle_index >= len(cycles):
                return "--"

            current_cycle = cycles[candle_index]
            cycle_type = current_cycle.cycle_type  # "bullish" or "bearish"

            # Calculate strength based on cycle length (same logic as peak/trough detection)
            # Find the start of the current cycle by looking backwards for a direction change
            cycle_start_index = 0
            for i in range(candle_index, -1, -1):
                if i < len(cycles) and cycles[i].direction_changed:
                    cycle_start_index = i
                    break

            # Calculate cycle length (strength) - this is the same as the strength calculation
            cycle_length = candle_index - cycle_start_index + 1

            # Use cycle length as strength (same as peak/trough strength calculation)
            if cycle_type == "bullish":
                return f"H{cycle_length}"
            elif cycle_type == "bearish":
                return f"L{cycle_length}"
            else:
                return "--"

        except Exception as e:
            self.logger.debug(f"Error calculating correct cycle position for index {candle_index}: {e}")
            return "--"

    def _calculate_cycle_position_backend(self, candle_index: int, cycles: list) -> str:
        """Calculate cycle position using C++ kernels only."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use C++ kernel for cycle position calculation
            result = indicators.calculate_cycle_position(candle_index, cycles)
            if result.success:
                return result.data
            else:
                raise RuntimeError(
                    f"C++ cycle position calculation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(f"C++ kernel error in cycle position calculation: {e}")
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _calculate_percentage_levels(self, bandwidth_values: list) -> dict:
        """Calculate percentage levels for bandwidth chart using C++ kernels only."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use C++ kernel for percentage levels calculation
            result = indicators.calculate_percentage_levels(bandwidth_values)
            if result.success:
                return result.data
            else:
                raise RuntimeError(
                    f"C++ percentage levels calculation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(f"C++ kernel error in percentage levels calculation: {e}")
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _calculate_zero_percent_price(self, rebased_data: list, closes: list) -> float:
        """Calculate zero percent price using C++ kernels only."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use C++ kernel for zero percent price calculation
            result = indicators.calculate_zero_percent_price(rebased_data, closes)
            if result.success:
                return float(result.data)
            else:
                raise RuntimeError(
                    f"C++ zero percent price calculation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(
                f"C++ kernel error in zero percent price calculation: {e}"
            )
            raise RuntimeError(f"C++ kernel error: {e}") from e

    def _prepare_peak_trough_business_data(self, peaks, troughs, current_pivot):
        """Prepare business logic data for peaks and troughs - Backend Logic Layer."""
        # This function only handles business logic, not visualization formatting
        business_data = {"peaks": [], "troughs": [], "current_pivot": current_pivot}

        # Process peaks - extract business logic data only
        for peak in peaks:
            peak_data = {
                "index": peak.index,
                "level": peak.level,
                "ray_end": peak.ray_end,
                "label": peak.label,
                "position": peak.position,
                "is_closed": peak.is_closed,
                "cycle_type": peak.cycle_type,
            }

            # Business logic: provide data for frontend display
            if current_pivot is not None:
                # Business logic: delegate calculation to C++ or provide raw data
                try:
                    # Try to use C++ computing function
                    if hasattr(indicators, "calculate_actual_price"):
                        result = indicators.calculate_actual_price(
                            current_pivot, peak.level
                        )
                        if result.success:
                            peak_data["actual_price"] = result.data
                        else:
                            # Fallback: provide raw data for frontend
                            peak_data["current_pivot"] = current_pivot
                            peak_data["level_percentage"] = peak.level
                    else:
                        # Fallback: provide raw data for frontend
                        peak_data["current_pivot"] = current_pivot
                        peak_data["level_percentage"] = peak.level
                except:
                    # Fallback: provide raw data for frontend
                    peak_data["current_pivot"] = current_pivot
                    peak_data["level_percentage"] = peak.level

            business_data["peaks"].append(peak_data)

        # Process troughs - extract business logic data only
        for trough in troughs:
            trough_data = {
                "index": trough.index,
                "level": trough.level,
                "ray_end": trough.ray_end,
                "label": trough.label,
                "position": trough.position,
                "is_closed": trough.is_closed,
                "cycle_type": trough.cycle_type,
            }

            # Business logic: provide data for frontend display
            if current_pivot is not None:
                # Business logic: delegate calculation to C++ or provide raw data
                try:
                    # Try to use C++ computing function
                    if hasattr(indicators, "calculate_actual_price"):
                        result = indicators.calculate_actual_price(
                            current_pivot, trough.level
                        )
                        if result.success:
                            trough_data["actual_price"] = result.data
                        else:
                            # Fallback: provide raw data for frontend
                            trough_data["current_pivot"] = current_pivot
                            trough_data["level_percentage"] = trough.level
                    else:
                        # Fallback: provide raw data for frontend
                        trough_data["current_pivot"] = current_pivot
                        trough_data["level_percentage"] = trough.level
                except:
                    # Fallback: provide raw data for frontend
                    trough_data["current_pivot"] = current_pivot
                    trough_data["level_percentage"] = trough.level

            business_data["troughs"].append(trough_data)

        return business_data

    def _prepare_serializable_data(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare serializable data for ProcessPoolExecutor using enterprise-grade buffer optimization."""
        serializable_data = {}

        for key, value in market_data.items():
            # Use buffer manager for optimal serialization
            if hasattr(value, "__array_interface__") or hasattr(value, "tolist"):
                # Use buffer manager to optimize array serialization
                serializable_data[key] = buffer_manager.optimize_data_for_serialization(
                    value
                )
            elif isinstance(value, (list, tuple)):
                # Handle lists that might contain C++ objects
                if key in ["cycles", "peaks", "troughs"]:
                    # Convert C++ objects to dictionaries
                    serializable_list = []
                    for item in value:
                        if hasattr(item, "__dict__"):
                            # Convert object to dict
                            serializable_list.append(vars(item))
                        elif hasattr(item, "index") and hasattr(item, "level"):
                            # Handle C++ objects with specific attributes
                            serializable_list.append(
                                {
                                    "index": getattr(item, "index", 0),
                                    "level": getattr(item, "level", 0.0),
                                    "ray_end": getattr(item, "ray_end", 0),
                                    "label": getattr(item, "label", ""),
                                    "position": getattr(item, "position", ""),
                                    "is_closed": getattr(item, "is_closed", False),
                                    "cycle_type": getattr(item, "cycle_type", ""),
                                }
                            )
                        else:
                            # Keep simple objects as-is
                            serializable_list.append(item)
                    serializable_data[key] = serializable_list
                else:
                    # Keep other lists as-is
                    serializable_data[key] = value
            elif isinstance(value, (str, int, float, bool, type(None))):
                # Keep serializable types as-is
                serializable_data[key] = value
            else:
                # Skip non-serializable objects
                continue

        return serializable_data

    # REMOVED: _prepare_data_table_rows_backend method
    # ALL calculations moved to indicators.cpp for zero-computation architecture
    # Backend only orchestrates C++ kernel calls - NO CALCULATIONS IN PYTHON

    async def fetch_market_data(
        self, ticker: str, timeframe: str, dtl: int, length: int
    ) -> Dict[str, Any]:
        """Fetch market data and calculate indicators."""
        task_id = f"{ticker}_{timeframe}_{dtl}_{length}_{datetime.now().timestamp()}"

        try:
            self.logger.info(f"Fetching data: {task_id}")

            # Create task for tracking
            task = asyncio.create_task(
                self._fetch_market_data_impl(ticker, timeframe, dtl, length)
            )
            self.active_tasks[task_id] = task

            result = await task

            self.logger.info(f"Data fetch completed: {task_id}")
            return result

        except Exception as e:
            self.logger.error(f"Error in fetch_market_data: {str(e)}")
            raise
        finally:
            # Remove completed task
            self.active_tasks.pop(task_id, None)

    async def _fetch_market_data_impl(
        self, ticker: str, timeframe: str, dtl: int, length: int
    ) -> Dict[str, Any]:
        """Implementation of market data fetching."""
        # Fetch data in thread executor (I/O-bound) to avoid blocking
        loop = asyncio.get_event_loop()
        data = await loop.run_in_executor(
            self.thread_executor, self._fetch_data_sync, ticker, timeframe, dtl
        )

        # Generate timestamps using C++ computing - no mathematical operations in backend
        data_length = len(data)
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required but not available")

        try:
            # Use C++ for array generation
            result = indicators.generate_timestamps(data_length)
            if result.success:
                timestamps = result.data
            else:
                raise RuntimeError(
                    f"C++ timestamp generation failed: {result.error_message}"
                )
        except Exception as e:
            self.logger.error(f"C++ kernel error in timestamp generation: {e}")
            raise RuntimeError(f"C++ kernel error: {e}") from e
        opens = data["Open"].values
        highs = data["High"].values
        lows = data["Low"].values
        closes = data["Close"].values
        volumes = data["Volume"].values

        # Calculate Donchian midpoint
        donchian_midpoint = self._calculate_donchian_midpoint(closes, length)

        # Prepare result
        result = {
            "ticker": ticker,
            "timeframe": timeframe,
            "dtl": dtl,
            "length": length,
            "timestamp": timestamps,
            "open": opens,
            "high": highs,
            "low": lows,
            "close": closes,
            "volume": volumes,
            "donchian_midpoint": donchian_midpoint,
            "fetch_time": datetime.now().isoformat(),
        }

        return result

    async def fetch_market_data_with_rebasing(
        self, ticker: str, timeframe: str, dtl: int, length: int, fwl_aggr: int = 1,
        viewing_historical: bool = False, historical_index: Optional[int] = None
    ) -> Dict[str, Any]:
        """Fetch market data and calculate rebasing with Donchian midpoint."""

        # Create correlation context for request tracing
        with trace_operation("fetch_market_data_with_rebasing") as context:
            task_id = f"{ticker}_{timeframe}_{dtl}_{length}_rebasing_{datetime.now().timestamp()}"

            # Add request metadata to context
            context.operation = f"fetch_data_rebasing_{ticker}_{timeframe}"

            try:
                self.logger.info(
                    f"Fetching data with rebasing: {task_id}",
                    ticker=ticker,
                    timeframe=timeframe,
                    dtl=dtl,
                    length=length,
                )

                # Track request metrics
                self._request_count += 1
                metrics_collector.increment_counter(
                    "data_requests_total",
                    labels={"operation": "fetch_with_rebasing", "ticker": ticker},
                )

                # Create task for tracking
                task = asyncio.create_task(
                    self._fetch_market_data_with_rebasing_impl(
                        ticker, timeframe, dtl, length, fwl_aggr, viewing_historical, historical_index
                    )
                )
                self.active_tasks[task_id] = task

                result = await task

                self.logger.info(
                    f"Data fetch with rebasing completed: {task_id}",
                    data_points=len(result.get("ohlc_data", [])),
                )

                metrics_collector.increment_counter(
                    "data_requests_success_total",
                    labels={"operation": "fetch_with_rebasing", "ticker": ticker},
                )
                return result

            except Exception as e:
                self._error_count += 1
                metrics_collector.increment_counter(
                    "data_requests_error_total",
                    labels={"operation": "fetch_with_rebasing", "ticker": ticker},
                )
                self.logger.error(
                    f"Error in fetch_market_data_with_rebasing: {str(e)}",
                    ticker=ticker,
                    error_type=type(e).__name__,
                )
                raise
            finally:
                # Remove completed task
                self.active_tasks.pop(task_id, None)

    async def _fetch_market_data_with_rebasing_impl(
        self, ticker: str, timeframe: str, dtl: int, length: int, fwl_aggr: int = 1,
        viewing_historical: bool = False, historical_index: Optional[int] = None
    ) -> Dict[str, Any]:
        """Implementation of market data fetching with rebasing."""

        # Removed artificial memory limiters – rely on actual capacity and error handling
        # No length/DTL/FWL hard caps here. Let compute kernels and system memory determine feasibility.
        # Fetch data in thread executor (I/O-bound) to avoid blocking
        loop = asyncio.get_event_loop()
        data = await loop.run_in_executor(
            self.thread_executor, self._fetch_data_sync, ticker, timeframe, dtl
        )

        # Extract data arrays using C++ computing - no mathematical operations in backend
        data_length = len(data)

        # No artificial caps on fetched size; log only
        if data_length > 100000:
            self.logger.info(f"Processing large dataset: {data_length} rows")
        if not CPP_KERNELS_AVAILABLE:
            # Fallback business logic: use numpy array for C++ compatibility
            timestamps = np.arange(data_length, dtype=np.float64)
        else:
            try:
                # Use C++ for array generation
                result = indicators.generate_timestamps(data_length)
                if result.success:
                    timestamps = result.data
                else:
                    # Fallback business logic: use numpy array for C++ compatibility
                    timestamps = np.arange(data_length, dtype=np.float64)
            except:
                # Fallback business logic: use numpy array for C++ compatibility
                timestamps = np.arange(data_length, dtype=np.float64)

        # Store actual dates separately for information display using zero-copy approach
        # Convert DatetimeIndex to string list for C++ compatibility
        if hasattr(data.index, 'strftime'):
            # pandas DatetimeIndex - convert to string format
            actual_dates = data.index.strftime('%Y-%m-%d').tolist()
        elif hasattr(data.index, 'tolist'):
            # Convert to list and then to strings
            date_list = data.index.tolist()
            actual_dates = []
            for date_obj in date_list:
                if hasattr(date_obj, 'strftime'):
                    actual_dates.append(date_obj.strftime('%Y-%m-%d'))
                else:
                    actual_dates.append(str(date_obj)[:10])  # Take first 10 chars
        else:
            actual_dates = [str(date)[:10] for date in data.index]
        opens = data["Open"].values
        highs = data["High"].values
        lows = data["Low"].values
        closes = data["Close"].values
        volumes = data["Volume"].values

        # Calculate Donchian midpoint (algorithm starts from index = length)
        donchian_midpoint = self._calculate_donchian_midpoint_with_skip(closes, length)

        # Calculate Donchian midpoint 50 for the indicator strip (algorithm starts from index 50)
        donchian_midpoint_50 = self._calculate_donchian_midpoint_with_skip(closes, 50)

        # Track vector direction changes
        cycles = self._track_vector_direction_changes(donchian_midpoint)

        # Calculate rebased prices using C++ computing layer
        rebased_data, rebased_vector = self._calculate_rebased_prices(
            opens, highs, lows, closes, donchian_midpoint, cycles
        )

        # Apply skip logic using backend logic layer
        rebased_data, rebased_vector = self._apply_skip_logic_to_rebased_prices(
            rebased_data, rebased_vector, length
        )

        # Calculate peaks and troughs using C++ kernels with updated algorithm
        peaks, troughs = self._calculate_peaks_and_troughs(rebased_data)

        # Calculate zero percent price for peak/trough calculations
        zero_percent_price = self._calculate_zero_percent_price(rebased_data, closes)

        # Prepare business data for peaks and troughs using zero percent price
        peak_trough_business_data = self._prepare_peak_trough_business_data(
            peaks, troughs, zero_percent_price
        )

        # Calculate Donchian indicator strip segments (only for non-skipped candles)
        donchian_strip_segments = self._calculate_donchian_strip_segments_with_skip(
            closes, donchian_midpoint_50, 50
        )

        # Calculate Bollinger Bandwidth with skip logic (algorithm starts from index = length)
        bollinger_bandwidth = self._calculate_bollinger_bandwidth_with_skip(
            closes, length
        )

        # Calculate standard deviations based on peaks and troughs levels
        standard_deviations = self._calculate_standard_deviations(peaks, troughs)

        # Calculate mean peak/trough strengths
        mean_peak_trough_strengths = self._calculate_mean_peak_trough_strengths(
            peaks, troughs
        )

        # Calculate mean peak/trough levels
        mean_peak_trough_levels_cpp, mean_peak_trough_levels = (
            self._calculate_mean_peak_trough_levels(peaks, troughs)
        )

        # Calculate predictive cycle lines (use C++ object for internal calls)
        predictive_cycle_lines = self._calculate_predictive_cycle_lines(
            peaks, troughs, mean_peak_trough_strengths, mean_peak_trough_levels_cpp
        )

        # Calculate percentile levels from peaks and troughs
        stdev_infinity_lines = self._calculate_stdev_infinity_lines(
            peaks, troughs, zero_percent_price
        )

        # Calculate crosshair info lookup
        crosshair_info_lookup = self._calculate_crosshair_info_lookup(
            rebased_data, cycles
        )

        # Calculate percentage levels for bandwidth chart
        percentage_levels = self._calculate_percentage_levels(bollinger_bandwidth)

        # Prepare base result
        base_result = {
            "ticker": ticker,
            "timeframe": timeframe,
            "dtl": dtl,
            "length": length,
            "timestamp": timestamps,
            "actual_dates": actual_dates,  # Add actual dates for information display
            "open": opens,
            "high": highs,
            "low": lows,
            "close": closes,
            "volume": volumes,
            "donchian_midpoint": donchian_midpoint,
            "donchian_midpoint_50": donchian_midpoint_50,
            "donchian_strip_segments": donchian_strip_segments,
            "bollinger_bandwidth": bollinger_bandwidth,
            "cycles": cycles,
            "rebased_data": rebased_data,
            "rebased_vector": rebased_vector,
            "peaks": peaks,
            "troughs": troughs,
            "peak_trough_business_data": peak_trough_business_data,
            "standard_deviations": standard_deviations,
            "mean_peak_trough_strengths": mean_peak_trough_strengths,
            "mean_peak_trough_levels": mean_peak_trough_levels,
            "predictive_cycle_lines": predictive_cycle_lines,
            "stdev_infinity_lines": stdev_infinity_lines,
            "crosshair_info_lookup": crosshair_info_lookup,
            "percentage_levels": percentage_levels,
            "zero_percent_price": zero_percent_price,
            "fetch_time": datetime.now().isoformat(),
        }

        # Prepare data table rows using C++ kernel (zero-computation UI)
        try:
            # Extract rebased percentage data for peak/trough detection
            rebased_opens = [item[1] for item in rebased_data]  # open_pct
            rebased_highs = [item[2] for item in rebased_data]  # high_pct
            rebased_lows = [item[3] for item in rebased_data]   # low_pct
            rebased_closes = [item[4] for item in rebased_data] # close_pct

            # Use C++ kernel for all table data preparation with rebased data for peak/trough detection
            table_rows = indicators.prepare_data_table_rows(
                timestamps, actual_dates, opens, highs, lows, closes, volumes,
                crosshair_info_lookup, length, rebased_opens, rebased_highs, rebased_lows, rebased_closes
            )

            # Convert py::list to Python list
            table_rows_list = list(table_rows)
            base_result["table_rows"] = table_rows_list

            # Enhanced logging for large dataset debugging
            if len(table_rows_list) == 0 and data_length > 0:
                self.logger.warning(f"C++ returned 0 table rows for {data_length} input rows - possible error in C++ processing")
            else:
                self.logger.info(f"Prepared {len(table_rows_list)} table rows in C++ from {data_length} input rows")

        except Exception as e:
            self.logger.error(f"Error preparing table rows in C++: {e}")
            # Fallback: provide empty table rows to prevent UI errors
            base_result["table_rows"] = []

        # Prepare projected OHLC table rows using C++ kernel (zero-computation UI)
        try:
            # Use C++ kernel for projected OHLC calculations
            projected_ohlc_rows = indicators.prepare_projected_ohlc_table_rows(
                actual_dates, opens, highs, lows, closes, crosshair_info_lookup
            )
            # Preserve raw projected OHLC rows before applying any FWL aggregation
            projected_ohlc_rows_base = list(projected_ohlc_rows)

            # Store current FWL Aggr value for UI access
            self.current_fwl_aggr = fwl_aggr

            # Apply FWL Aggr logic for the selected FWL value
            if fwl_aggr > 1:
                self.logger.info(f"Applying FWL Aggr logic with value: {fwl_aggr}")
                projected_ohlc_rows = self._apply_fwl_aggr_logic(
                    projected_ohlc_rows, highs, lows, closes, crosshair_info_lookup, fwl_aggr,
                    viewing_historical, historical_index
                )
                self.logger.info(f"FWL Aggr logic applied successfully")
            else:
                self.logger.info(f"Applying FWL Aggr logic with value: {fwl_aggr} (FWL=1)")
                # For FWL=1, we still need to generate metadata for the UI
                projected_ohlc_rows = self._apply_fwl_aggr_logic(
                    projected_ohlc_rows, highs, lows, closes, crosshair_info_lookup, fwl_aggr,
                    viewing_historical, historical_index
                )
                self.logger.info(f"FWL Aggr logic applied for FWL=1")

            # Store the metadata for the selected FWL value
            selected_fwl_metadata = {}
            if hasattr(self, 'fwl_aggr_metadata'):
                selected_fwl_metadata = self.fwl_aggr_metadata.copy()



            # Restore the metadata for the selected FWL value (not the 5-FWL metadata)
            self.fwl_aggr_metadata = selected_fwl_metadata

            # Store FWL Aggr value in market data for UI access
            base_result["fwl_aggr_value"] = fwl_aggr

            # Store FWL Aggr metadata (PH/PL values per row) for UI access
            if hasattr(self, 'fwl_aggr_metadata'):
                base_result["fwl_aggr_metadata"] = self.fwl_aggr_metadata
            else:
                base_result["fwl_aggr_metadata"] = {}

            # Convert py::list to Python list
            projected_ohlc_rows_list = list(projected_ohlc_rows)

            # If C++ returned a different count than input, apply Python fallback (same correction as OHLC mode)
            if len(projected_ohlc_rows_list) != data_length and data_length > 0:
                self.logger.warning("C++ returned mismatched projected OHLC rows — using Python fallback")
                try:
                    latest_close = (
                        float(closes[historical_index]) if (viewing_historical and historical_index is not None and historical_index < len(closes))
                        else (float(closes[-1]) if len(closes) > 0 else 0.0)
                    )
                    py_rows = []
                    for i in range(data_length):
                        # Date string (already normalized to YYYY-MM-DD above)
                        date_str = actual_dates[i] if i < len(actual_dates) else "N/A"
                        # Weekday
                        try:
                            weekday = datetime.strptime(date_str, "%Y-%m-%d").strftime("%A") if len(date_str) >= 10 else "Unknown"
                        except Exception:
                            weekday = "Unknown"
                        # Category from crosshair lookup
                        cat = ""
                        info = crosshair_info_lookup.get(str(i)) if isinstance(crosshair_info_lookup, dict) else None
                        if isinstance(info, dict):
                            cat = str(info.get("category", ""))
                            if "⇨" in cat:
                                cat = cat.split("⇨")[0]
                        row = [date_str, weekday, cat]
                        if i < data_length - 1:
                            current_open = float(opens[i])
                            current_close = float(closes[i])
                            next_open = float(opens[i + 1])
                            next_high = float(highs[i + 1])
                            next_low = float(lows[i + 1])
                            next_close = float(closes[i + 1])
                            dollar_change_high = next_high - current_close
                            dollar_change_low = next_low - current_close
                            percent_change_high = (dollar_change_high / current_close) * 100.0 if current_close else 0.0
                            percent_change_low = (dollar_change_low / current_close) * 100.0 if current_close else 0.0
                            projected_high_change = (percent_change_high * latest_close) / 100.0
                            projected_low_change = (percent_change_low * latest_close) / 100.0
                            projected_high = latest_close + projected_high_change
                            projected_low = latest_close + projected_low_change
                            # Open ratio: next row open / current row open
                            open_ratio = (next_open / current_open) if current_open != 0.0 else 0.0
                            # Close ratio: next row close / current row close
                            close_ratio = (next_close / current_close) if current_close != 0.0 else 0.0
                            row.extend([
                                f"{dollar_change_high:.4f}",
                                f"{dollar_change_low:.4f}",
                                f"{percent_change_high:.2f}",
                                f"{percent_change_low:.2f}",
                                f"{projected_high_change:.2f}",
                                f"{projected_low_change:.2f}",
                                f"{projected_high:.2f}",
                                f"{projected_low:.2f}",
                                f"{open_ratio:.6f}",
                                f"{close_ratio:.6f}",
                            ])
                        else:
                            # Last row has no forward data
                            row.extend(["", "", "", "", "", "", "", "", "", ""])  # keep column count consistent
                        # Colors
                        bg_color = ""
                        fg_color = "#FFFFFF"
                        if cat:
                            if cat.startswith("H"):
                                bg_color = "#1B4D3E"


                            elif cat.startswith("L"):
                                bg_color = "#4D1B1B"
                        row.extend([bg_color, fg_color])
                        py_rows.append(row)
                    projected_ohlc_rows_list = py_rows
                    # Keep base rows aligned with fallback-generated rows
                    projected_ohlc_rows_base = list(py_rows)

                    self.logger.info(f"Prepared {len(projected_ohlc_rows_list)} projected OHLC table rows in Python fallback")
                except Exception as e_fallback:
                    self.logger.error(f"Python fallback for projected OHLC failed: {e_fallback}")
                    projected_ohlc_rows_list = []

            base_result["projected_ohlc_table_rows"] = projected_ohlc_rows_list
            # Expose the raw (unaggregated) projected OHLC rows for consistent downstream use
            base_result["projected_ohlc_table_rows_base"] = projected_ohlc_rows_base

            self.logger.info(f"Prepared {len(projected_ohlc_rows_list)} projected OHLC table rows in C++ or fallback")

            # Also prepare a 5-FWL aggregated variant based on current price/close (as in volatility stats)
            try:
                # Preserve currently selected FWL metadata
                _selected_fwl_metadata_snapshot = (
                    self.fwl_aggr_metadata.copy() if hasattr(self, 'fwl_aggr_metadata') and isinstance(self.fwl_aggr_metadata, dict) else {}
                )

                # Apply FWL aggregation with fixed window = 5 using same historical/current close logic
                projected_ohlc_rows_5fwl = self._apply_fwl_aggr_logic(
                    projected_ohlc_rows_base, highs, lows, closes, crosshair_info_lookup, 5,
                    viewing_historical, historical_index
                )

                # Capture 5-FWL PH/PL metadata for UI consumers
                fwl5_metadata = (
                    self.fwl_aggr_metadata.copy() if hasattr(self, 'fwl_aggr_metadata') and isinstance(self.fwl_aggr_metadata, dict) else {}
                )

                # Attach new dataset without disturbing currently-selected FWL dataset/metadata
                base_result["projected_ohlc_table_rows_5fwl"] = list(projected_ohlc_rows_5fwl)
                base_result["fwl_aggr_metadata_5"] = fwl5_metadata

                # Restore the metadata snapshot for the selected FWL value
                self.fwl_aggr_metadata = _selected_fwl_metadata_snapshot

                self.logger.info(f"Prepared {len(projected_ohlc_rows_5fwl)} projected OHLC rows for fixed 5-FWL dataset")
            except Exception as e:
                self.logger.error(f"Error preparing 5-FWL projected OHLC dataset: {e}")
                base_result["projected_ohlc_table_rows_5fwl"] = []
                base_result["fwl_aggr_metadata_5"] = {}


        except Exception as e:
            self.logger.error(f"Error preparing projected OHLC table rows in C++: {e}")
            # Apply the same Python fallback used to unblock OHLC mode
            try:
                latest_close = (
                    float(closes[historical_index]) if (viewing_historical and historical_index is not None and historical_index < len(closes))
                    else (float(closes[-1]) if len(closes) > 0 else 0.0)
                )
                py_rows = []
                for i in range(data_length):
                    # Date string (already normalized to YYYY-MM-DD above)
                    date_str = actual_dates[i] if i < len(actual_dates) else "N/A"
                    # Weekday
                    try:


                        weekday = datetime.strptime(date_str, "%Y-%m-%d").strftime("%A") if len(date_str) >= 10 else "Unknown"
                    except Exception:
                        weekday = "Unknown"
                    # Category from crosshair lookup
                    cat = ""
                    info = crosshair_info_lookup.get(str(i)) if isinstance(crosshair_info_lookup, dict) else None
                    if isinstance(info, dict):
                        cat = str(info.get("category", ""))
                        if "⇨" in cat:
                            cat = cat.split("⇨")[0]
                    row = [date_str, weekday, cat]
                    if i < data_length - 1:
                        current_open = float(opens[i])
                        current_close = float(closes[i])
                        next_open = float(opens[i + 1])
                        next_high = float(highs[i + 1])
                        next_low = float(lows[i + 1])
                        next_close = float(closes[i + 1])
                        dollar_change_high = next_high - current_close
                        dollar_change_low = next_low - current_close
                        percent_change_high = (dollar_change_high / current_close) * 100.0 if current_close else 0.0
                        percent_change_low = (dollar_change_low / current_close) * 100.0 if current_close else 0.0
                        projected_high_change = (percent_change_high * latest_close) / 100.0
                        projected_low_change = (percent_change_low * latest_close) / 100.0
                        projected_high = latest_close + projected_high_change


                        projected_low = latest_close + projected_low_change
                        # Open ratio: next row open / current row open
                        open_ratio = (next_open / current_open) if current_open != 0.0 else 0.0
                        # Close ratio: next row close / current row close
                        close_ratio = (next_close / current_close) if current_close != 0.0 else 0.0
                        row.extend([
                            f"{dollar_change_high:.4f}",
                            f"{dollar_change_low:.4f}",
                            f"{percent_change_high:.2f}",
                            f"{percent_change_low:.2f}",
                            f"{projected_high_change:.2f}",
                            f"{projected_low_change:.2f}",
                            f"{projected_high:.2f}",
                            f"{projected_low:.2f}",
                            f"{open_ratio:.6f}",
                            f"{close_ratio:.6f}",
                        ])
                    else:
                        # Last row has no forward data
                        row.extend(["", "", "", "", "", "", "", "", "", ""])  # keep column count consistent
                    # Colors
                    bg_color = ""
                    fg_color = "#FFFFFF"
                    if cat:
                        if cat.startswith("H"):
                            bg_color = "#1B4D3E"
                        elif cat.startswith("L"):
                            bg_color = "#4D1B1B"
                    row.extend([bg_color, fg_color])
                    py_rows.append(row)
                base_result["projected_ohlc_table_rows"] = py_rows
                # Keep base rows aligned with fallback-generated rows
                base_result["projected_ohlc_table_rows_base"] = py_rows

                self.logger.info(f"Prepared {len(py_rows)} projected OHLC table rows in Python fallback after C++ error")
            except Exception as e_fallback:
                self.logger.error(f"Python fallback for projected OHLC failed: {e_fallback}")
                base_result["projected_ohlc_table_rows"] = []

        # Prepare volatility statistics data using C++ kernel (zero-computation UI)
        try:
            volatility_stats_data = self._prepare_volatility_statistics_data(
                base_result["projected_ohlc_table_rows"]
            )
            base_result.update(volatility_stats_data)
        except Exception as e:
            self.logger.error(f"Error preparing volatility statistics data: {e}")
            # Fallback: provide empty volatility statistics data
            base_result["volatility_high_data"] = []

            base_result["volatility_low_data"] = []
            base_result["volatility_filtered_high_data"] = []
            base_result["volatility_filtered_low_data"] = []

        # Also prepare volatility statistics from fixed 5-FWL projected data for Weekly Zones (if available)
        try:
            if "projected_ohlc_table_rows_5fwl" in base_result and base_result["projected_ohlc_table_rows_5fwl"]:
                fwl5_stats = self._prepare_volatility_statistics_data(
                    base_result["projected_ohlc_table_rows_5fwl"]
                )
                base_result["volatility_high_data_5fwl"] = fwl5_stats.get("volatility_high_data", [])
                base_result["volatility_low_data_5fwl"] = fwl5_stats.get("volatility_low_data", [])
                base_result["volatility_filtered_high_data_5fwl"] = fwl5_stats.get("volatility_filtered_high_data", [])
                base_result["volatility_filtered_low_data_5fwl"] = fwl5_stats.get("volatility_filtered_low_data", [])
        except Exception as e:
            self.logger.error(f"Error preparing 5-FWL volatility statistics data: {e}")
            base_result["volatility_high_data_5fwl"] = []
            base_result["volatility_low_data_5fwl"] = []
            base_result["volatility_filtered_high_data_5fwl"] = []
            base_result["volatility_filtered_low_data_5fwl"] = []

        # Ensure consistency: if current FWL Aggr is 5, force 5-FWL datasets to match current datasets
        try:
            if fwl_aggr == 5:
                base_result["volatility_high_data_5fwl"] = base_result.get("volatility_high_data", [])
                base_result["volatility_low_data_5fwl"] = base_result.get("volatility_low_data", [])
                base_result["volatility_filtered_high_data_5fwl"] = base_result.get("volatility_filtered_high_data", [])
                base_result["volatility_filtered_low_data_5fwl"] = base_result.get("volatility_filtered_low_data", [])
                # Also align PH/PL metadata for 5-FWL
                if "fwl_aggr_metadata" in base_result:
                    base_result["fwl_aggr_metadata_5"] = base_result.get("fwl_aggr_metadata", {})
        except Exception:
            pass

        # Prepare visualization data in main process (avoid pickling issues with C++ objects)
        # TODO: Implement proper serialization for C++ objects to enable ProcessPoolExecutor

        # Prepare the fixed 5-FWL dataset in fallback path as well
        try:
            _selected_fwl_metadata_snapshot_fb = (
                self.fwl_aggr_metadata.copy() if hasattr(self, 'fwl_aggr_metadata') and isinstance(self.fwl_aggr_metadata, dict) else {}
            )
            projected_ohlc_rows_5fwl_fb = self._apply_fwl_aggr_logic(
                base_result.get("projected_ohlc_table_rows_base", base_result.get("projected_ohlc_table_rows", [])), highs, lows, closes, crosshair_info_lookup, 5,
                viewing_historical, historical_index
            )
            fwl5_metadata_fb = (
                self.fwl_aggr_metadata.copy() if hasattr(self, 'fwl_aggr_metadata') and isinstance(self.fwl_aggr_metadata, dict) else {}
            )
            base_result["projected_ohlc_table_rows_5fwl"] = list(projected_ohlc_rows_5fwl_fb)
            base_result["fwl_aggr_metadata_5"] = fwl5_metadata_fb
            # Restore selected-FWL metadata snapshot
            self.fwl_aggr_metadata = _selected_fwl_metadata_snapshot_fb
            self.logger.info(f"Prepared {len(projected_ohlc_rows_5fwl_fb)} projected OHLC rows for fixed 5-FWL dataset (fallback)")
        except Exception as e_fb:
            self.logger.error(f"Error preparing 5-FWL dataset in fallback: {e_fb}")
            base_result["projected_ohlc_table_rows_5fwl"] = []
            base_result["fwl_aggr_metadata_5"] = {}

        from .visualization_worker import prepare_visualization_data_worker

        # Prepare serializable data
        serializable_data = self._prepare_serializable_data(base_result)

        # Run in main process to avoid pickling issues




        visualization_data = prepare_visualization_data_worker(serializable_data)

        # Merge base result with visualization data
        base_result.update(visualization_data)
        return base_result

    def _prepare_volatility_statistics_data(self, projected_ohlc_rows: list) -> dict:
        """Prepare volatility statistics data using C++ compute layer - Backend Logic Layer."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required for volatility statistics processing")

        try:
            # Extract and format high/low data using C++ kernel
            extracted_data = indicators.extract_volatility_statistics_data(projected_ohlc_rows)

            high_data = list(extracted_data["high_data"])
            low_data = list(extracted_data["low_data"])

            # Apply default filter (H/L matching) in Python backend to avoid requiring a C++ rebuild
            default_filter_result = self._apply_volatility_statistics_filter_py(
                high_data, low_data, 0  # 0 = H/L matching filter by default
            )

            return {
                "volatility_high_data": high_data,
                "volatility_low_data": low_data,
                "volatility_filtered_high_data": list(default_filter_result["filtered_high"]),
                "volatility_filtered_low_data": list(default_filter_result["filtered_low"]),
            }

        except Exception as e:
            self.logger.error(f"Error in volatility statistics data preparation: {e}")
            raise RuntimeError(f"Volatility statistics processing failed: {e}") from e

    def apply_volatility_statistics_filter(self, high_data: list, low_data: list, filter_type: int) -> dict:
        """Apply volatility statistics filter using C++ compute layer - Backend Logic Layer."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels are required for volatility statistics filtering")

        try:
            # Use Python implementation to avoid requiring a native rebuild.
            py_result = self._apply_volatility_statistics_filter_py(high_data, low_data, filter_type)
            return {
                "volatility_filtered_high_data": py_result["filtered_high"],
                "volatility_filtered_low_data": py_result["filtered_low"],
            }

        except Exception as e:
            self.logger.error(f"Error in volatility statistics filtering: {e}")
            raise RuntimeError(f"Volatility statistics filtering failed: {e}") from e


    def _apply_volatility_statistics_filter_py(self, high_data: list, low_data: list, filter_type: int) -> dict:
        """Pure-Python fallback for volatility statistics filtering matching C++ semantics.
        Structure of each row: [Index, Weekday, Category, $Change, %Change, $Projected Change, Projected]
        - For H/L matching (0): match by Category at column 2
        - For Weekday matching (1): match by Weekday at column 1
        - No filter (-1): return originals
        """
        try:
            if filter_type == -1 or not high_data or not low_data:
                return {"filtered_high": high_data, "filtered_low": low_data}

            last_high_row = high_data[-1]

            if filter_type == 0:
                # H/L Matching by Category (column 2)
                if len(last_high_row) > 2:
                    last_category = str(last_high_row[2])
                    filtered_high = [row for row in high_data if len(row) > 2 and str(row[2]) == last_category]
                    filtered_low = [row for row in low_data if len(row) > 2 and str(row[2]) == last_category]
                else:
                    filtered_high, filtered_low = high_data, low_data
            elif filter_type == 1:
                # Weekday Matching by Weekday (column 1)
                if len(last_high_row) > 1:
                    last_weekday = str(last_high_row[1])
                    filtered_high = [row for row in high_data if len(row) > 1 and str(row[1]) == last_weekday]
                    filtered_low = [row for row in low_data if len(row) > 1 and str(row[1]) == last_weekday]
                else:
                    filtered_high, filtered_low = high_data, low_data
            else:
                filtered_high, filtered_low = high_data, low_data

            return {"filtered_high": filtered_high, "filtered_low": filtered_low}
        except Exception:
            # On any error, fall back to originals
            return {"filtered_high": high_data, "filtered_low": low_data}

    def _apply_fwl_aggr_logic(self, projected_ohlc_rows, highs, lows, closes, crosshair_info_lookup, fwl_aggr,
                             viewing_historical=False, historical_index=None):
        """Apply forward-looking window aggregation logic to projected OHLC rows."""
        try:
            import numpy as np

            self.logger.info(f"Starting FWL Aggr processing with window size: {fwl_aggr}")

            # Convert to numpy arrays for easier processing
            highs_array = np.array(highs)
            lows_array = np.array(lows)
            closes_array = np.array(closes)
            data_length = len(closes_array)

            # Determine the close price to use for projections
            if viewing_historical and historical_index is not None and historical_index < len(closes_array):
                # Use historical close price for projections when in historical mode
                projection_close = closes_array[historical_index]
                self.logger.info(f"Using historical close price for FWL projections: ${projection_close:.2f} (index {historical_index})")
            else:
                # Use latest close price for projections (normal mode)
                projection_close = closes_array[-1]
                self.logger.info(f"Using latest close price for FWL projections: ${projection_close:.2f}")

            latest_close = projection_close  # Keep variable name for compatibility

            # Process each row
            modified_rows = []
            for i, row in enumerate(projected_ohlc_rows):
                row_list = list(row)  # Convert to list for modification

                # Debug: Log first row structure
                if i == 0:
                    self.logger.info(f"First row structure: {len(row_list)} columns")
                    self.logger.info(f"First row content: {row_list[:min(5, len(row_list))]}")

                # Only modify rows that have enough forward-looking data
                if i < data_length - 1:  # Need at least 1 day forward
                    current_close = closes_array[i]

                    # Initialize tracking variables - use current close as baseline
                    farthest_high = current_close
                    farthest_low = current_close
                    ph_days = 0  # Days forward to farthest high
                    pl_days = 0  # Days forward to farthest low

                    # Look ahead within the FWL Aggr window to find extremes
                    max_look_ahead = min(fwl_aggr, data_length - i - 1)

                    # Collect all forward data points
                    forward_data = []
                    for j in range(1, max_look_ahead + 1):
                        forward_data.append({
                            'days_ahead': j,
                            'high': highs_array[i + j],
                            'low': lows_array[i + j]
                        })

                    if forward_data:
                        # Find the farthest high and low relative to the base close price
                        # For highs: find the one with maximum distance above base_close
                        farthest_high_data = max(forward_data, key=lambda d: d['high'] - current_close)
                        # For lows: find the one with maximum distance below base_close (most negative difference)
                        farthest_low_data = min(forward_data, key=lambda d: d['low'] - current_close)

                        farthest_high = farthest_high_data['high']
                        farthest_low = farthest_low_data['low']
                        ph_days = farthest_high_data['days_ahead']
                        pl_days = farthest_low_data['days_ahead']

                    # Always update the row with FWL Aggr calculations
                    # Recalculate projected values using farthest high/low
                    dollar_change_high = farthest_high - current_close
                    dollar_change_low = farthest_low - current_close
                    percent_change_high = (dollar_change_high / current_close) * 100.0
                    percent_change_low = (dollar_change_low / current_close) * 100.0
                    projected_high_change = (percent_change_high * latest_close) / 100.0
                    projected_low_change = (percent_change_low * latest_close) / 100.0
                    projected_high = latest_close + projected_high_change
                    projected_low = latest_close + projected_low_change

                    # Update the row with new values - Backend column structure (no "Updated" column)
                    # Backend columns: Date(0), Weekday(1), Category(2), $Change High(3), $Change Low(4),
                    #                  %Change High(5), %Change Low(6), $Projected High Change(7),
                    #                  $Projected Low Change(8), Projected High(9), Projected Low(10), bg_color(11), fg_color(12)
                    if len(row_list) >= 11:
                        row_list[3] = f"{dollar_change_high:.2f}"  # $Change High
                        row_list[4] = f"{dollar_change_low:.2f}"   # $Change Low
                        row_list[5] = f"{percent_change_high:.2f}" # %Change High
                        row_list[6] = f"{percent_change_low:.2f}"  # %Change Low
                        row_list[7] = f"{projected_high_change:.2f}" # $Projected High Change
                        row_list[8] = f"{projected_low_change:.2f}"  # $Projected Low Change
                        row_list[9] = f"{projected_high:.2f}"      # Projected High
                        row_list[10] = f"{projected_low:.2f}"      # Projected Low

                    # Always store PH and PL values for UI display
                    if not hasattr(self, 'fwl_aggr_metadata'):
                        self.fwl_aggr_metadata = {}
                    self.fwl_aggr_metadata[i] = {'ph': ph_days, 'pl': pl_days}

                    # Log the PH/PL values for debugging
                    if i < 5:  # Log first few rows for debugging
                        self.logger.info(f"Row {i}: PH:{ph_days} PL:{pl_days} (farthest_high: {farthest_high:.2f}, farthest_low: {farthest_low:.2f})")
                else:
                    # For rows without forward data, store 0 values
                    if not hasattr(self, 'fwl_aggr_metadata'):
                        self.fwl_aggr_metadata = {}
                    self.fwl_aggr_metadata[i] = {'ph': 0, 'pl': 0}

                modified_rows.append(row_list)

            self.logger.info(f"FWL Aggr processing completed. Modified {len(modified_rows)} rows with window size {fwl_aggr}")
            return modified_rows

        except Exception as e:
            self.logger.error(f"Error applying FWL Aggr logic: {e}")
            return projected_ohlc_rows  # Return original rows on error

    def get_current_fwl_aggr(self):
        """Get the current FWL Aggr value for UI display."""
        return getattr(self, 'current_fwl_aggr', 1)
