"""
Main Window for DataDriven Trading Application
Manages the main UI with universal controls and tabbed interface.
"""

from PyQt6.QtWidgets import (
    QMainWindow,
    QWidget,
    QVBoxLayout,
    QTabWidget,
    QStatusBar,
    QMenuBar,
    QMenu,
    QTabBar,
    QStackedWidget,
)
from PyQt6.QtCore import Qt, QTimer, pyqtSlot
from PyQt6.QtGui import (
    QAction,
    QIcon,
    QFont,
    QPainter,
    QPen,
    QColor,
    QPainterPath,
    QBrush,
    QLinearGradient,
    QPixmap,
)

from .universal_controls import UniversalControls
from .market_odds import MarketOddsTab
from .option_analyzer import OptionAnalyzerTab
from .data_tab import DataTab
from .volatility_statistics import VolatilityStatisticsTab
from .backtester import BacktesterTab

# Import enterprise-grade logging
from backend.observability import get_structured_logger


class CustomTabBar(QTabBar):
    """Custom tab bar with Google-style design, rounded top corners, and optimized rendering."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDrawBase(False)

        # Set font with medium weight for better visibility
        font = QFont("Segoe UI", 8)
        font.setWeight(QFont.Weight.Medium)
        self.setFont(font)

        # Make all tabs the same width
        self.setExpanding(False)  # Don't expand tabs to fill space

        # Cache for rendered tab pixmaps to avoid redrawing
        self._tab_pixmap_cache = {}
        self._last_current_index = -1

    def tabSizeHint(self, index):
        """Return consistent size for all tabs."""
        size = super().tabSizeHint(index)
        # Set fixed width for all tabs
        size.setWidth(150)  # Fixed width for all tabs
        return size

    def paintEvent(self, event):
        """Optimized paint event with region-restricted repaints and cached pixmaps."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Get the update region to only repaint what's needed
        update_region = event.region()
        current_index = self.currentIndex()

        # Check if current tab changed to invalidate cache
        if current_index != self._last_current_index:
            # Clear cache when selection changes
            self._tab_pixmap_cache.clear()
            self._last_current_index = current_index

        # Paint only tabs that intersect with the update region
        for index in range(self.count()):
            tab_rect = self.tabRect(index)
            if update_region.intersects(tab_rect):
                self.paintTab(painter, index)

    def paintTab(self, painter, index):
        """Paint a single tab with cached pixmaps for better performance."""
        rect = self.tabRect(index)
        selected = index == self.currentIndex()

        # Create cache key based on tab state and size
        cache_key = (index, selected, rect.width(), rect.height())

        # Check if we have a cached pixmap for this tab state
        if cache_key in self._tab_pixmap_cache:
            pixmap = self._tab_pixmap_cache[cache_key]
            painter.drawPixmap(rect.topLeft(), pixmap)
            return

        # Create pixmap for caching
        pixmap = QPixmap(rect.size())
        pixmap.fill(Qt.GlobalColor.transparent)

        # Use try-finally to ensure painter cleanup
        pixmap_painter = QPainter(pixmap)
        try:
            pixmap_painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # Adjust rect for pixmap coordinates
            pixmap_rect = rect.translated(-rect.topLeft())

            if selected:
                # Create custom shape with rounded top corners and extended bottom to connect with content
                path = QPainterPath()
                corner_radius = 4  # Smaller radius for subtle effect

                # Extend the selected tab down by 1 pixel to connect with content pane
                extended_rect = pixmap_rect.adjusted(0, 0, 0, 1)

                # Start from bottom left (flat corner, extended)
                path.moveTo(extended_rect.left(), extended_rect.bottom())
                # Line to top left, then arc for rounded top-left corner
                path.lineTo(extended_rect.left(), extended_rect.top() + corner_radius)
                path.arcTo(
                    extended_rect.left(),
                    extended_rect.top(),
                    corner_radius * 2,
                    corner_radius * 2,
                    180,
                    -90,
                )
                # Line across top to start of top-right corner
                path.lineTo(extended_rect.right() - corner_radius, extended_rect.top())
                # Arc for rounded top-right corner
                path.arcTo(
                    extended_rect.right() - corner_radius * 2,
                    extended_rect.top(),
                    corner_radius * 2,
                    corner_radius * 2,
                    90,
                    -90,
                )
                # Line down to bottom right (flat corner, extended)
                path.lineTo(extended_rect.right(), extended_rect.bottom())
                # Close path back to bottom left
                path.lineTo(extended_rect.left(), extended_rect.bottom())

                # Fill the custom shape with base color
                pixmap_painter.fillPath(path, QBrush(QColor("#1e1e1e")))

                # Apply gradient within the clipped region (top fade only)
                pixmap_painter.setClipPath(path)

                # Create vertical gradient for top fade using #2b2b2b color
                v_gradient = QLinearGradient(
                    0, extended_rect.top(), 0, extended_rect.bottom()
                )
                v_gradient.setColorAt(
                    0.0, QColor(43, 43, 43, 150)
                )  # #2b2b2b with high opacity at top
                v_gradient.setColorAt(
                    0.15, QColor(43, 43, 43, 100)
                )  # #2b2b2b with medium opacity
                v_gradient.setColorAt(
                    0.4, QColor(43, 43, 43, 30)
                )  # #2b2b2b with low opacity
                v_gradient.setColorAt(
                    1.0, QColor(43, 43, 43, 0)
                )  # #2b2b2b fully transparent at bottom
                pixmap_painter.fillRect(extended_rect, QBrush(v_gradient))

                # Reset clipping
                pixmap_painter.setClipping(False)

                text_color = QColor("#ffffff")  # White text for selected
            else:
                # Unselected tab - simple flat background
                pixmap_painter.fillRect(pixmap_rect, QBrush(QColor("#2b2b2b")))
                text_color = QColor("#e0e0e0")  # Brighter grey text for unselected

            # Draw text with better positioning
            pixmap_painter.setPen(text_color)
            text_rect = pixmap_rect.adjusted(
                12, 2, -12, -2
            )  # Reduced padding, added vertical margins
            pixmap_painter.drawText(
                text_rect,
                Qt.AlignmentFlag.AlignVCenter | Qt.AlignmentFlag.AlignLeft,
                self.tabText(index),
            )

            # Draw separator lines between unselected tabs (25% to 75% height)
            if not selected and index < self.count() - 1:
                next_selected = (index + 1) == self.currentIndex()
                if not next_selected:  # Only draw if next tab is also unselected
                    separator_pen = QPen(QColor("#555555"), 1)
                    pixmap_painter.setPen(separator_pen)

                    x = pixmap_rect.right()
                    tab_height = pixmap_rect.height()
                    y_start = pixmap_rect.top() + int(tab_height * 0.25)
                    y_end = pixmap_rect.top() + int(tab_height * 0.75)

                    pixmap_painter.drawLine(x, y_start, x, y_end)

        finally:
            # Ensure pixmap painter is always properly ended
            if pixmap_painter.isActive():
                pixmap_painter.end()

        # Cache the pixmap
        self._tab_pixmap_cache[cache_key] = pixmap

        # Draw the cached pixmap
        painter.drawPixmap(rect.topLeft(), pixmap)


class MainWindow(QMainWindow):
    """Main application window with universal controls and tabbed interface."""

    def __init__(self, data_service, parent=None):
        super().__init__(parent)
        self.data_service = data_service
        self.logger = get_structured_logger("MainWindow")
        self.setup_ui()
        self.setup_connections()
        # Remove status bar - not needed

        # Performance optimization: defer non-critical initialization
        QTimer.singleShot(100, self.post_init_setup)

    def setup_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle("Market Odds")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 700)

        # Set Segoe UI font for the entire application
        font = QFont("Segoe UI")
        self.setFont(font)

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Universal controls at the top - Centralized Data Management
        self.universal_controls = UniversalControls(self.data_service)
        main_layout.addWidget(self.universal_controls)

        # Tab widget with custom tab bar for separator lines
        self.tab_widget = QTabWidget()

        # Replace default tab bar with custom one
        custom_tab_bar = CustomTabBar()
        self.tab_widget.setTabBar(custom_tab_bar)

        # Set minimal styling to ensure tab pane connects with selected tab
        self.tab_widget.setStyleSheet(
            """
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #1e1e1e;
                border-top: none;
            }
        """
        )

        # Set tab height
        custom_tab_bar.setFixedHeight(26)

        # Enterprise-grade lazy loading with QStackedWidget for efficient view management
        self.tabs = {}
        self.tab_constructors = {
            0: lambda: MarketOddsTab(self.data_service),
            1: lambda: OptionAnalyzerTab(),
            2: lambda: VolatilityStatisticsTab(self.data_service),  # Volatility Statistics tab
            3: lambda: DataTab(),
            4: lambda: BacktesterTab(),  # Backtester tab
        }

        # Create stacked widget for efficient view swapping without teardown
        self.stacked_widget = QStackedWidget()

        # Add placeholder tabs - actual widgets created on first access
        self.tab_widget.addTab(QWidget(), "Market Odds")
        self.tab_widget.addTab(QWidget(), "Option Analyzer")
        self.tab_widget.addTab(QWidget(), "Volatility Statistics")
        self.tab_widget.addTab(QWidget(), "Data")
        self.tab_widget.addTab(QWidget(), "Backtester")

        # Load ALL tabs immediately to ensure centralized data system works
        # This ensures all tabs are connected to Universal Controls from startup
        for i in range(len(self.tab_constructors)):
            self._load_tab(i)
            self.logger.info(f"Loaded tab {i} for centralized data system")

        # Set up cross-tab references for FWL Aggr functionality
        self.universal_controls.set_main_window(self)

        # Set Market Odds as the current tab for user interaction
        self.tab_widget.setCurrentIndex(0)

        main_layout.addWidget(self.tab_widget)

        # Remove menu bar - not needed

    # Menu bar removed for cleaner interface

    def _load_tab(self, index):
        """Lazy load a tab when first accessed."""
        if index in self.tabs:
            return  # Already loaded

        if index in self.tab_constructors:
            # Get tab text before removing
            tab_text = self.tab_widget.tabText(index)

            # Create the actual tab widget
            tab_widget = self.tab_constructors[index]()
            self.tabs[index] = tab_widget

            # Store specific tab references for cross-tab access
            if index == 2:  # Volatility Statistics tab
                self.volatility_statistics_tab = tab_widget
                # Set main window reference for FWL Aggr functionality
                if hasattr(tab_widget, 'set_main_window_ref'):
                    tab_widget.set_main_window_ref(self)

            # Replace the placeholder
            self.tab_widget.removeTab(index)
            self.tab_widget.insertTab(index, tab_widget, tab_text)

            # Setup connections for this tab
            self._setup_tab_connections(index, tab_widget)

    def _on_tab_changed(self, index):
        """Handle tab change - load tab if not already loaded."""
        self._load_tab(index)

    def _setup_tab_connections(self, index, tab_widget):
        """
        Setup connections for a specific tab using centralized data management.
        All tabs now receive data automatically from Universal Controls.
        """
        # Connect tab to centralized data system if it has the required methods
        if hasattr(tab_widget, 'on_data_received'):
            # MarketOddsTab and similar tabs with on_data_received method
            self.universal_controls.signals.connect_threadsafe(
                self.universal_controls.signals.market_data.chart_data_updated,
                tab_widget.on_data_received,
                Qt.ConnectionType.QueuedConnection,
            )
            self.logger.info(f"Tab {index} connected to centralized data system via on_data_received")

        elif hasattr(tab_widget, 'on_market_data_updated'):
            # DataTab and similar tabs with on_market_data_updated method
            self.universal_controls.signals.connect_threadsafe(
                self.universal_controls.signals.market_data.chart_data_updated,
                tab_widget.on_market_data_updated,
                Qt.ConnectionType.QueuedConnection,
            )
            self.logger.info(f"Tab {index} connected to centralized data system via on_market_data_updated")

        elif hasattr(tab_widget, 'update_data'):
            # VolatilityStatisticsTab and similar tabs with update_data method
            self.universal_controls.signals.connect_threadsafe(
                self.universal_controls.signals.market_data.chart_data_updated,
                tab_widget.update_data,
                Qt.ConnectionType.QueuedConnection,
            )
            self.logger.info(f"Tab {index} connected to centralized data system via update_data")

        else:
            self.logger.info(f"Tab {index} has no data update method - skipping centralized connection")

    def setup_connections(self):
        """Setup signal connections between components."""
        # Connect tab change handler for lazy loading
        self.tab_widget.currentChanged.connect(self._on_tab_changed)

        # Initial connections for loaded tabs will be set up in _setup_tab_connections

    # Status bar removed for cleaner interface

    def post_init_setup(self):
        """Perform non-critical initialization after main window is shown."""
        # Any additional setup that can be deferred
        pass

    # Removed menu/status bar related methods

    def closeEvent(self, event):
        """Handle application close event."""
        # Graceful shutdown
        if hasattr(self, "data_service") and self.data_service:
            # Signal backend to shutdown
            pass
        event.accept()
