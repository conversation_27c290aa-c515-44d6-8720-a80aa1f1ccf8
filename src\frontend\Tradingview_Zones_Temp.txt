//@version=6
indicator('anyticker Zones & Levels Export', overlay = true, max_boxes_count = 500, max_lines_count = 500)

// Generated on date
// Exported 28 zones and 30 levels from candlestick chart

// Variables to store boxes, lines and labels
var box_array = array.new<box>()
var line_array = array.new<line>()
var label_array = array.new<label>()

// Clear previous items to prevent trail
if barstate.islast
    if array.size(box_array) > 0
        for i = 0 to array.size(box_array) - 1 by 1
            box.delete(array.get(box_array, i))
        array.clear(box_array)
    if array.size(line_array) > 0
        for i = 0 to array.size(line_array) - 1 by 1
            line.delete(array.get(line_array, i))
        array.clear(line_array)
    if array.size(label_array) > 0
        for i = 0 to array.size(label_array) - 1 by 1
            label.delete(array.get(label_array, i))
        array.clear(label_array)

// HL average_high (Updated_Vol) from 6589.4237 to 6593.5763
if barstate.islast
    zone_box = box.new(bar_index - 100, 6589.4237, bar_index + 100, 6593.5763, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6591.5000, 
                          text = 'HL average_high', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// HL average_low (Updated_Vol) from 6329.0057 to 6332.9943
if barstate.islast
    zone_box = box.new(bar_index - 100, 6329.0057, bar_index + 100, 6332.9943, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6331.0000, 
                          text = 'HL average_low', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// HL lowest_high (Updated_Vol) from 6409.9802 to 6414.0198
if barstate.islast
    zone_box = box.new(bar_index - 100, 6409.9802, bar_index + 100, 6414.0198, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6412.0000, 
                          text = 'HL lowest_high', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// HL highest_low (Updated_Vol) from 6408.4807 to 6412.5193
if barstate.islast
    zone_box = box.new(bar_index - 100, 6408.4807, bar_index + 100, 6412.5193, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6410.5000, 
                          text = 'HL highest_low', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// HL maxavg_high (Updated_Vol) from 6690.1419 to 6694.3581
if barstate.islast
    zone_box = box.new(bar_index - 100, 6690.1419, bar_index + 100, 6694.3581, 
                      border_color = color.rgb(0, 188, 212), bgcolor = color.new(color.rgb(0, 188, 212), 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6692.2500, 
                          text = 'HL maxavg_high', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.rgb(0, 188, 212), 
                          size = size.small)
    array.push(label_array, zone_label)

// HL maxavg_low (Updated_Vol) from 6282.0205 to 6285.9795
if barstate.islast
    zone_box = box.new(bar_index - 100, 6282.0205, bar_index + 100, 6285.9795, 
                      border_color = color.rgb(0, 188, 212), bgcolor = color.new(color.rgb(0, 188, 212), 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6284.0000, 
                          text = 'HL maxavg_low', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.rgb(0, 188, 212), 
                          size = size.small)
    array.push(label_array, zone_label)

// HL true_avg_high (Updated_Vol) from 6589.4237 to 6593.5763
if barstate.islast
    zone_box = box.new(bar_index - 100, 6589.4237, bar_index + 100, 6593.5763, 
                      border_color = color.rgb(76, 175, 80), bgcolor = color.new(color.rgb(76, 175, 80), 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6591.5000, 
                          text = 'HL true_avg_high', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.rgb(76, 175, 80), 
                          size = size.small)
    array.push(label_array, zone_label)

// HL true_avg_low (Updated_Vol) from 6329.0057 to 6332.9943
if barstate.islast
    zone_box = box.new(bar_index - 100, 6329.0057, bar_index + 100, 6332.9943, 
                      border_color = color.rgb(76, 175, 80), bgcolor = color.new(color.rgb(76, 175, 80), 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6331.0000, 
                          text = 'HL true_avg_low', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.rgb(76, 175, 80), 
                          size = size.small)
    array.push(label_array, zone_label)

// HL avg_high_lowest_high (Updated_Vol) from 6468.9616 to 6473.0384
if barstate.islast
    zone_box = box.new(bar_index - 100, 6468.9616, bar_index + 100, 6473.0384, 
                      border_color = color.rgb(255, 235, 59), bgcolor = color.new(color.rgb(255, 235, 59), 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6471.0000, 
                          text = 'HL avg_high_lowest_high', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.rgb(255, 235, 59), 
                          size = size.small)
    array.push(label_array, zone_label)

// HL avg_low_highest_low (Updated_Vol) from 6347.0001 to 6350.9999
if barstate.islast
    zone_box = box.new(bar_index - 100, 6347.0001, bar_index + 100, 6350.9999, 
                      border_color = color.rgb(255, 235, 59), bgcolor = color.new(color.rgb(255, 235, 59), 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6349.0000, 
                          text = 'HL avg_low_highest_low', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.rgb(255, 235, 59), 
                          size = size.small)
    array.push(label_array, zone_label)

// HL median_apex_highest_high (Updated_Vol) from 6845.3430 to 6849.6570
if barstate.islast
    zone_box = box.new(bar_index - 100, 6845.3430, bar_index + 100, 6849.6570, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6847.5000, 
                          text = 'HL median_apex_highest_high', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// HL median_apex_lowest_low (Updated_Vol) from 6430.9736 to 6435.0264
if barstate.islast
    zone_box = box.new(bar_index - 100, 6430.9736, bar_index + 100, 6435.0264, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6433.0000, 
                          text = 'HL median_apex_lowest_low', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// HL wall_long (Updated_Vol) from 6690.1419 to 6694.3581
if barstate.islast
    zone_box = box.new(bar_index - 100, 6690.1419, bar_index + 100, 6694.3581, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6692.2500, 
                          text = 'HL wall_long', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// HL wall_short (Updated_Vol) from 6282.0205 to 6285.9795
if barstate.islast
    zone_box = box.new(bar_index - 100, 6282.0205, bar_index + 100, 6285.9795, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6284.0000, 
                          text = 'HL wall_short', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY average_high (Updated_Vol) from 6445.9689 to 6450.0311
if barstate.islast
    zone_box = box.new(bar_index - 100, 6445.9689, bar_index + 100, 6450.0311, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6448.0000, 
                          text = 'WEEKDAY average_high', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY average_low (Updated_Vol) from 6363.2449 to 6367.2551
if barstate.islast
    zone_box = box.new(bar_index - 100, 6363.2449, bar_index + 100, 6367.2551, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6365.2500, 
                          text = 'WEEKDAY average_low', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY lowest_high (Updated_Vol) from 6263.7763 to 6267.7237
if barstate.islast
    zone_box = box.new(bar_index - 100, 6263.7763, bar_index + 100, 6267.7237, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6265.7500, 
                          text = 'WEEKDAY lowest_high', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY highest_low (Updated_Vol) from 6449.7177 to 6453.7823
if barstate.islast
    zone_box = box.new(bar_index - 100, 6449.7177, bar_index + 100, 6453.7823, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6451.7500, 
                          text = 'WEEKDAY highest_low', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY maxavg_high (Updated_Vol) from 6476.2094 to 6480.2906
if barstate.islast
    zone_box = box.new(bar_index - 100, 6476.2094, bar_index + 100, 6480.2906, 
                      border_color = color.rgb(0, 188, 212), bgcolor = color.new(color.rgb(0, 188, 212), 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6478.2500, 
                          text = 'WEEKDAY maxavg_high', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.rgb(0, 188, 212), 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY maxavg_low (Updated_Vol) from 6323.2575 to 6327.2425
if barstate.islast
    zone_box = box.new(bar_index - 100, 6323.2575, bar_index + 100, 6327.2425, 
                      border_color = color.rgb(0, 188, 212), bgcolor = color.new(color.rgb(0, 188, 212), 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6325.2500, 
                          text = 'WEEKDAY maxavg_low', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.rgb(0, 188, 212), 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY true_avg_high (Updated_Vol) from 6445.9689 to 6450.0311
if barstate.islast
    zone_box = box.new(bar_index - 100, 6445.9689, bar_index + 100, 6450.0311, 
                      border_color = color.rgb(76, 175, 80), bgcolor = color.new(color.rgb(76, 175, 80), 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6448.0000, 
                          text = 'WEEKDAY true_avg_high', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.rgb(76, 175, 80), 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY true_avg_low (Updated_Vol) from 6363.2449 to 6367.2551
if barstate.islast
    zone_box = box.new(bar_index - 100, 6363.2449, bar_index + 100, 6367.2551, 
                      border_color = color.rgb(76, 175, 80), bgcolor = color.new(color.rgb(76, 175, 80), 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6365.2500, 
                          text = 'WEEKDAY true_avg_low', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.rgb(76, 175, 80), 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY avg_high_lowest_high (Updated_Vol) from 6448.7180 to 6452.7820
if barstate.islast
    zone_box = box.new(bar_index - 100, 6448.7180, bar_index + 100, 6452.7820, 
                      border_color = color.rgb(255, 235, 59), bgcolor = color.new(color.rgb(255, 235, 59), 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6450.7500, 
                          text = 'WEEKDAY avg_high_lowest_high', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.rgb(255, 235, 59), 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY avg_low_highest_low (Updated_Vol) from 6386.9875 to 6391.0125
if barstate.islast
    zone_box = box.new(bar_index - 100, 6386.9875, bar_index + 100, 6391.0125, 
                      border_color = color.rgb(255, 235, 59), bgcolor = color.new(color.rgb(255, 235, 59), 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6389.0000, 
                          text = 'WEEKDAY avg_low_highest_low', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.rgb(255, 235, 59), 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY median_apex_highest_high (Updated_Vol) from 6437.4716 to 6441.5284
if barstate.islast
    zone_box = box.new(bar_index - 100, 6437.4716, bar_index + 100, 6441.5284, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6439.5000, 
                          text = 'WEEKDAY median_apex_highest_high', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY median_apex_lowest_low (Updated_Vol) from 6151.3117 to 6155.1883
if barstate.islast
    zone_box = box.new(bar_index - 100, 6151.3117, bar_index + 100, 6155.1883, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6153.2500, 
                          text = 'WEEKDAY median_apex_lowest_low', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY wall_long (Updated_Vol) from 6476.2094 to 6480.2906
if barstate.islast
    zone_box = box.new(bar_index - 100, 6476.2094, bar_index + 100, 6480.2906, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6478.2500, 
                          text = 'WEEKDAY wall_long', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// WEEKDAY wall_short (Updated_Vol) from 6323.2575 to 6327.2425
if barstate.islast
    zone_box = box.new(bar_index - 100, 6323.2575, bar_index + 100, 6327.2425, 
                      border_color = color.gray, bgcolor = color.new(color.gray, 85), 
                      border_width = 1, extend = extend.both)
    array.push(box_array, zone_box)
    zone_label = label.new(bar_index, 6325.2500, 
                          text = 'WEEKDAY wall_short', style = label.style_none, 
                          color = color.new(color.white, 100), textcolor = color.gray, 
                          size = size.small)
    array.push(label_array, zone_label)

// HL average_high Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL average_high Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// HL average_low Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL average_low Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// HL lowest_high Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL lowest_high Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// HL highest_low Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL highest_low Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// HL maxavg_high Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.rgb(0, 188, 212), width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL maxavg_high Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.rgb(0, 188, 212), 
                           size = size.small)
    array.push(label_array, level_label)

// HL maxavg_low Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.rgb(0, 188, 212), width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL maxavg_low Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.rgb(0, 188, 212), 
                           size = size.small)
    array.push(label_array, level_label)

// HL minavg_high (Updated_Minavg) at 6450.5000
if barstate.islast
    level_line = line.new(bar_index - 100, 6450.5000, bar_index + 100, 6450.5000, 
                         color = color.white, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 6450.5000, 
                           text = 'HL minavg_high: 6450.5000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.white, 
                           size = size.small)
    array.push(label_array, level_label)

// HL minavg_low (Updated_Minavg) at 6383.7500
if barstate.islast
    level_line = line.new(bar_index - 100, 6383.7500, bar_index + 100, 6383.7500, 
                         color = color.white, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 6383.7500, 
                           text = 'HL minavg_low: 6383.7500', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.white, 
                           size = size.small)
    array.push(label_array, level_label)

// HL true_avg_high Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.rgb(76, 175, 80), width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL true_avg_high Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.rgb(76, 175, 80), 
                           size = size.small)
    array.push(label_array, level_label)

// HL true_avg_low Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.rgb(76, 175, 80), width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL true_avg_low Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.rgb(76, 175, 80), 
                           size = size.small)
    array.push(label_array, level_label)

// HL avg_high_lowest_high Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.rgb(255, 235, 59), width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL avg_high_lowest_high Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.rgb(255, 235, 59), 
                           size = size.small)
    array.push(label_array, level_label)

// HL avg_low_highest_low Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.rgb(255, 235, 59), width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL avg_low_highest_low Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.rgb(255, 235, 59), 
                           size = size.small)
    array.push(label_array, level_label)

// HL median_apex_highest_high Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL median_apex_highest_high Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// HL median_apex_lowest_low Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL median_apex_lowest_low Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// HL wall_long Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL wall_long Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// HL wall_short Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'HL wall_short Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY average_high Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY average_high Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY average_low Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY average_low Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY lowest_high Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY lowest_high Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY highest_low Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY highest_low Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY maxavg_high Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.rgb(0, 188, 212), width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY maxavg_high Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.rgb(0, 188, 212), 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY maxavg_low Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.rgb(0, 188, 212), width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY maxavg_low Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.rgb(0, 188, 212), 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY true_avg_high Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.rgb(76, 175, 80), width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY true_avg_high Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.rgb(76, 175, 80), 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY true_avg_low Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.rgb(76, 175, 80), width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY true_avg_low Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.rgb(76, 175, 80), 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY avg_high_lowest_high Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.rgb(255, 235, 59), width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY avg_high_lowest_high Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.rgb(255, 235, 59), 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY avg_low_highest_low Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.rgb(255, 235, 59), width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY avg_low_highest_low Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.rgb(255, 235, 59), 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY median_apex_highest_high Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY median_apex_highest_high Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY median_apex_lowest_low Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY median_apex_lowest_low Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY wall_long Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY wall_long Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// WEEKDAY wall_short Center Line (Vol_Center_Line) at 0.0000
if barstate.islast
    level_line = line.new(bar_index - 100, 0.0000, bar_index + 100, 0.0000, 
                         color = color.gray, width = 2, extend = extend.both)
    array.push(line_array, level_line)
    level_label = label.new(bar_index + 50, 0.0000, 
                           text = 'WEEKDAY wall_short Center Line: 0.0000', style = label.style_none, 
                           color = color.new(color.white, 100), textcolor = color.gray, 
                           size = size.small)
    array.push(label_array, level_label)

// Summary: Generated 28 zones and 30 levels
// Items included in this script:
// ZONES:
// - HL average_high: 6589.4237 to 6593.5763
// - HL average_low: 6329.0057 to 6332.9943
// - HL lowest_high: 6409.9802 to 6414.0198
// - HL highest_low: 6408.4807 to 6412.5193
// - HL maxavg_high: 6690.1419 to 6694.3581
// - HL maxavg_low: 6282.0205 to 6285.9795
// - HL true_avg_high: 6589.4237 to 6593.5763
// - HL true_avg_low: 6329.0057 to 6332.9943
// - HL avg_high_lowest_high: 6468.9616 to 6473.0384
// - HL avg_low_highest_low: 6347.0001 to 6350.9999
// - HL median_apex_highest_high: 6845.3430 to 6849.6570
// - HL median_apex_lowest_low: 6430.9736 to 6435.0264
// - HL wall_long: 6690.1419 to 6694.3581
// - HL wall_short: 6282.0205 to 6285.9795
// - WEEKDAY average_high: 6445.9689 to 6450.0311
// - WEEKDAY average_low: 6363.2449 to 6367.2551
// - WEEKDAY lowest_high: 6263.7763 to 6267.7237
// - WEEKDAY highest_low: 6449.7177 to 6453.7823
// - WEEKDAY maxavg_high: 6476.2094 to 6480.2906
// - WEEKDAY maxavg_low: 6323.2575 to 6327.2425
// - WEEKDAY true_avg_high: 6445.9689 to 6450.0311
// - WEEKDAY true_avg_low: 6363.2449 to 6367.2551
// - WEEKDAY avg_high_lowest_high: 6448.7180 to 6452.7820
// - WEEKDAY avg_low_highest_low: 6386.9875 to 6391.0125
// - WEEKDAY median_apex_highest_high: 6437.4716 to 6441.5284
// - WEEKDAY median_apex_lowest_low: 6151.3117 to 6155.1883
// - WEEKDAY wall_long: 6476.2094 to 6480.2906
// - WEEKDAY wall_short: 6323.2575 to 6327.2425
// LEVELS:
// - HL average_high Center Line: 0.0000
// - HL average_low Center Line: 0.0000
// - HL lowest_high Center Line: 0.0000
// - HL highest_low Center Line: 0.0000
// - HL maxavg_high Center Line: 0.0000
// - HL maxavg_low Center Line: 0.0000
// - HL minavg_high: 6450.5000
// - HL minavg_low: 6383.7500
// - HL true_avg_high Center Line: 0.0000
// - HL true_avg_low Center Line: 0.0000
// - HL avg_high_lowest_high Center Line: 0.0000
// - HL avg_low_highest_low Center Line: 0.0000
// - HL median_apex_highest_high Center Line: 0.0000
// - HL median_apex_lowest_low Center Line: 0.0000
// - HL wall_long Center Line: 0.0000
// - HL wall_short Center Line: 0.0000
// - WEEKDAY average_high Center Line: 0.0000
// - WEEKDAY average_low Center Line: 0.0000
// - WEEKDAY lowest_high Center Line: 0.0000
// - WEEKDAY highest_low Center Line: 0.0000
// - WEEKDAY maxavg_high Center Line: 0.0000
// - WEEKDAY maxavg_low Center Line: 0.0000
// - WEEKDAY true_avg_high Center Line: 0.0000
// - WEEKDAY true_avg_low Center Line: 0.0000
// - WEEKDAY avg_high_lowest_high Center Line: 0.0000
// - WEEKDAY avg_low_highest_low Center Line: 0.0000
// - WEEKDAY median_apex_highest_high Center Line: 0.0000
// - WEEKDAY median_apex_lowest_low Center Line: 0.0000
// - WEEKDAY wall_long Center Line: 0.0000
// - WEEKDAY wall_short Center Line: 0.0000